# DrCode: UI Testing - AI-Powered Browser Automation & Testing

## Overview
**DrCode: UI Testing** is a comprehensive AI-powered browser automation and UI testing platform delivered as a Chrome extension with a robust backend API. It combines advanced AI agents, vector database technology, and intelligent automation to revolutionize web testing and browser automation workflows.

## 🚀 Key Features

### 🤖 AI-Powered Automation
- **Lang<PERSON>hain Integration**: Advanced AI agents for intelligent task execution
- **Multi-Model Support**: Gemini, OpenAI, Anthropic, Groq, and Ollama providers
- **Vision Capabilities**: AI can see and understand web page content
- **Natural Language Processing**: Execute complex tasks described in plain English
- **Context Awareness**: Maintains context across multi-step automation tasks

### 🎯 Browser Automation
- **DOM Manipulation**: Click, type, scroll, and interact with web elements
- **Navigation Control**: Advanced page navigation and tab management
- **Element Selection**: Intelligent element identification and interaction
- **Screenshot Capture**: Visual documentation of automation steps
- **Real-time Feedback**: Live updates during task execution

### 📝 Playwright Script Generation
- **Action Tracking**: Automatic recording of all browser interactions
- **Multi-Language Support**: Generate scripts in JavaScript, TypeScript, Python, Java, C#
- **Customizable Output**: Include comments, assertions, and wait statements
- **Re-execution**: Replay recorded actions without AI agents
- **Export Functionality**: Download generated scripts as files

### 🧪 Test Case Generation & Execution
- **AI-Generated Tests**: Create comprehensive test cases using critical thinking
- **Multiple Categories**: Security, performance, accessibility, compatibility testing
- **Real Browser Testing**: Execute tests in actual browser environment
- **Visual Evidence**: Screenshot capture and detailed execution logging
- **Test Management**: Complete CRUD operations for test cases

### 🗄️ Vector Database & RAG
- **Pinecone Integration**: Production-ready vector storage with namespace isolation
- **Gemini Embeddings**: Advanced semantic search capabilities
- **Hierarchical Structure**: User → Project → File → Chunks organization
- **Document Processing**: Support for PDF, DOCX, TXT, and MD files
- **Auto-Refinement**: AI-powered prompt improvement using vector context

### 🎨 User Interface
- **Side Panel**: Primary testing interface that persists across tabs
- **Options Page**: Comprehensive settings and configuration management
- **Content Scripts**: Non-intrusive page interaction capabilities
- **Dark/Light Themes**: Customizable UI themes
- **Responsive Design**: Works across different screen sizes

## Project Architecture

### Monorepo Structure
This is a **monorepo** managed with **pnpm workspaces** and **Turbo** for efficient build orchestration and dependency management.

```
drcode-ui-testing/
├── backend/                    # Python FastAPI backend
├── chrome-extension/           # Extension manifest and configuration
├── packages/                   # Shared workspace packages
├── pages/                      # Extension UI pages
└── dist/                      # Built extension files
```

### Technology Stack

#### Frontend & Extension
- **TypeScript** - Type-safe development across all components
- **React** - Modern UI framework for extension interfaces
- **Vite** - Lightning-fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework for consistent styling
- **LangChain** - AI agent framework for intelligent automation
- **Chrome Extension APIs** - Native browser integration

#### Backend & AI
- **Python 3.12+** - Backend services and AI processing
- **FastAPI** - Modern, fast web framework for building APIs
- **Google Gemini** - Primary AI model for embeddings and chat
- **Pinecone** - Production vector database for semantic search
- **Uvicorn** - Lightning-fast ASGI server

#### Development & Build
- **Turbo** - Monorepo orchestration and intelligent caching
- **pnpm** - Fast, disk space efficient package manager
- **ESLint & Prettier** - Code quality and formatting
- **TypeScript** - Static type checking across the entire codebase

## Component Overview

### `/backend` - Python FastAPI Backend
**Purpose**: Core API server providing AI-powered services

**Key Features**:
- **Auto-Refinement Service**: AI-powered prompt improvement using vector context
- **Vector Database Management**: Pinecone integration with Gemini embeddings
- **Test Case Generation**: AI-generated comprehensive UI test cases
- **Document Evaluation**: Intelligent document analysis and scoring
- **Health Monitoring**: API health checks and token usage tracking
- **AI Model Proxy**: OpenAI-compatible API proxy for extension compatibility

**Technologies**: Python 3.12+, FastAPI, Uvicorn, Pinecone, Google Gemini

### `/chrome-extension` - Extension Configuration
**Purpose**: Chrome extension manifest and core configuration

**Key Features**:
- **Manifest V3**: Modern Chrome extension architecture
- **Multi-Browser Support**: Chrome, Firefox, and Opera compatibility
- **Dynamic Configuration**: Environment-based manifest generation
- **Permission Management**: Comprehensive permission declarations
- **Cross-Browser Compatibility**: Handles browser-specific differences

### `/packages` - Shared Workspace Packages
**Purpose**: Reusable components and utilities across the monorepo

**Packages**:
- **`@extension/storage`**: Type-safe Chrome storage abstraction with live updates
- **`@extension/ui`**: React component library with design system
- **`@extension/shared`**: Common utilities, hooks, and helper functions
- **`@extension/dev-utils`**: Development tools and debugging utilities
- **`@extension/i18n`**: Internationalization support and translations
- **`@extension/tsconfig`**: Shared TypeScript configurations
- **`@extension/tailwind-config`**: Shared Tailwind CSS configuration
- **`@extension/vite-config`**: Shared Vite build configurations

### `/pages` - Extension UI Pages
**Purpose**: User interface components for different extension contexts

**Pages**:
- **`side-panel/`**: Primary testing interface with AI chat and automation controls
- **`options/`**: Comprehensive settings page with AI provider configuration
- **`content/`**: Content scripts for web page interaction and DOM manipulation

**Features**:
- **React-based**: Modern React applications with TypeScript
- **Shared Dependencies**: Leverages workspace packages for consistency
- **Theme Support**: Dark/light theme compatibility
- **Responsive Design**: Optimized for different screen sizes and contexts

## 🚀 Quick Start

### Prerequisites
- **Node.js** >= 22.12.0
- **pnpm** >= 9.15.1
- **Python** 3.12+ (for backend services)
- **Chrome Browser** (for extension development)

### 1. Clone & Install
```bash
# Clone the repository
git clone https://github.com/airia-in/ui-testing-extension.git
cd ui-testing-extension

# Install all dependencies
pnpm install
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env

# Edit .env with your API keys
# GEMINI_API_KEY=your_gemini_api_key
# PINECONE_API_KEY=your_pinecone_api_key

# Start the backend server
python start_server.py
```

### 3. Extension Development
```bash
# Return to root directory
cd ..

# Start development build (Chrome)
pnpm build
```

### 4. Load Extension in Browser
1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select the `dist/` directory
4. The extension should now appear in your browser

## Development Workflow

### Available Scripts

#### Extension Development
```bash
pnpm dev                    # Chrome development with hot reload
pnpm dev:firefox           # Firefox development build
pnpm build                 # Production Chrome build
pnpm build:firefox         # Production Firefox build
pnpm zip                   # Create Chrome extension package
pnpm zip:firefox           # Create Firefox extension package
```

#### Code Quality
```bash
pnpm type-check            # TypeScript type checking
pnpm lint                  # ESLint code linting
pnpm lint:fix              # Auto-fix linting issues
pnpm prettier              # Format code with Prettier
```

#### Backend Development
```bash
cd backend
python main.py             # Start backend server
python start_server.py     # Alternative startup script
uvicorn main:app --reload  # Development with auto-reload
```

#### Testing
```bash
pnpm e2e                   # End-to-end tests (when available)
pnpm test                  # Run all tests
```

### Environment Configuration

#### Backend Environment Variables
Create `backend/.env` with:
```bash
# AI Provider Configuration
GEMINI_API_KEY=your_gemini_api_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=drcode

# OpenAI Compatibility Layer
OPENAI_API_KEY=your_gemini_api_key
OPENAI_API_URL=https://generativelanguage.googleapis.com/v1beta/openai

# Model Configuration
DEFAULT_PLAN_MODEL=gemini-2.5-flash
DEFAULT_NAVIGATION_MODEL=gemini-2.5-flash
DEFAULT_VALIDATION_MODEL=gemini-2.5-flash
```

#### Extension Configuration
The extension automatically detects the backend at `http://localhost:8001`. You can configure this in the extension's settings page after installation.

## 📖 Usage Examples

### AI-Powered Browser Automation
```
1. Open the extension side panel
2. Type: "Fill out the contact form with test data and submit it"
3. Watch as the AI agent intelligently:
   - Identifies form fields
   - Fills appropriate test data
   - Handles validation
   - Submits the form
   - Captures screenshots
```

### Test Case Generation
```
1. Navigate to the page you want to test
2. Open the extension and describe the functionality:
   "A login form with username, password, and remember me checkbox"
3. The AI generates comprehensive test cases covering:
   - Security vulnerabilities (XSS, SQL injection)
   - Accessibility compliance
   - Performance edge cases
   - Cross-browser compatibility
   - User experience scenarios
```

### Playwright Script Generation
```
1. Perform any actions on a webpage (clicking, typing, navigating)
2. All actions are automatically recorded
3. Generate scripts in your preferred language:
   - JavaScript/TypeScript
   - Python
   - Java
   - C#
4. Export and integrate into your testing pipeline
```

### Document-Based Automation
```
1. Upload project documentation (PDFs, Word docs, etc.)
2. Ask questions like: "How should I test the payment flow?"
3. Get contextual answers based on your documentation
4. Generate tests that align with your specifications
```

## 📚 Documentation

### Core Documentation
- **[Backend API Documentation](backend/API_DOCUMENTATION.md)** - Complete API reference
- **[Authentication System](AUTHENTICATION.md)** - Login and session management
- **[Vector Database](backend/VECTOR_DB_NEW_SCHEMA.md)** - Document storage and retrieval
- **[Extension Architecture](chrome-extension/ARCHITECTURE.md)** - Technical architecture overview

### Component Documentation
- **[Backend README](backend/README.md)** - Backend setup and features
- **[Chrome Extension README](chrome-extension/README.md)** - Extension configuration
- **[Playwright Script Generation](chrome-extension/PLAYWRIGHT_README.md)** - Script generation features
- **[Test Case Generation](backend/TESTCASE_GENERATOR_README.md)** - AI-powered test creation

### Package Documentation
- **[Storage Package](packages/storage/README.md)** - Data persistence layer
- **[UI Components](packages/ui/README.md)** - Reusable UI components
- **[Shared Utilities](packages/shared/README.md)** - Common utilities and hooks

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow the development setup instructions above
4. Make your changes and test thoroughly
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Code Standards
- **TypeScript**: All new code should be written in TypeScript
- **ESLint**: Follow the established linting rules
- **Prettier**: Use Prettier for code formatting
- **Testing**: Add tests for new functionality
- **Documentation**: Update documentation for new features

### Areas for Contribution
- **AI Model Integration**: Add support for new AI providers
- **Browser Compatibility**: Extend support to more browsers
- **Test Categories**: Add new types of automated tests
- **UI Improvements**: Enhance user interface and experience
- **Performance**: Optimize automation and search performance

## 🐛 Troubleshooting

### Common Issues

#### Extension Not Loading
- Ensure you've run `pnpm build` before loading
- Check Chrome Developer Tools for console errors
- Verify all required permissions are granted

#### Backend Connection Issues
- Confirm backend is running on `http://localhost:8001`
- Check API keys are correctly configured in `.env`
- Verify firewall/network settings allow local connections

#### AI Features Not Working
- Validate API keys for Gemini and Pinecone
- Check API quotas and rate limits
- Ensure internet connection is stable

#### Vector Database Issues
- Verify Pinecone index exists and is accessible
- Check embedding model availability
- Confirm namespace permissions

### Getting Help
- **Issues**: Report bugs on [GitHub Issues](https://github.com/airia-in/ui-testing-extension/issues)
- **Discussions**: Join conversations on [GitHub Discussions](https://github.com/airia-in/ui-testing-extension/discussions)
- **Documentation**: Check the comprehensive documentation in this repository

## 📄 License
This project is licensed under the **Apache-2.0 License** - see the [LICENSE](LICENSE) file for details.

## 🔗 Links
- **Repository**: https://github.com/airia-in/ui-testing-extension
- **DrCode Platform**: https://www.drcode.ai
- **Chrome Web Store**: Coming Soon
- **Firefox Add-ons**: Coming Soon

---

**Built with ❤️ for better UI testing and automation**
