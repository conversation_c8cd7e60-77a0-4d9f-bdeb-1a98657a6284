# DrCode: UI Testing - Authentication System

## Overview
The DrCode: UI Testing extension implements a comprehensive authentication system that integrates with the DrCode web platform to provide secure user authentication, session management, and project-based access control.

## Architecture

### Components
```
┌─────────────────────────────────────────────────────────────────┐
│                    Authentication Flow                          │
├─────────────────────────────────────────────────────────────────┤
│  Extension UI (Side Panel/Options)                             │
│  ├── LoginComponent                                            │
│  └── ProjectSelector                                           │
├─────────────────────────────────────────────────────────────────┤
│  Background Service Worker                                      │
│  ├── LoginService                                              │
│  └── Message Handlers                                          │
├─────────────────────────────────────────────────────────────────┤
│  Storage Layer                                                 │
│  ├── LoginStore                                                │
│  └── Chrome Storage API                                        │
├─────────────────────────────────────────────────────────────────┤
│  External Integration                                          │
│  ├── DrCode Web Platform (https://www.drcode.ai)               │
│  └── Backend API Headers                                       │
└─────────────────────────────────────────────────────────────────┘
```

## Authentication Flow

### 1. Login Process
```
User clicks "Login" → Extension opens DrCode login page → User authenticates on web platform → Extension monitors tab for completion → Extracts session data → Stores in extension storage → Updates UI state
```

#### Detailed Steps:
1. **Login Initiation**: User clicks login button in extension UI
2. **Tab Creation**: Extension opens `https://www.drcode.ai/login` in new tab
3. **User Authentication**: User completes login on DrCode web platform
4. **Session Monitoring**: Extension monitors the tab for login completion
5. **Data Extraction**: Extension extracts session data from localStorage
6. **Storage**: Session data is stored in Chrome extension storage
7. **UI Update**: Extension UI updates to show logged-in state

### 2. Session Management
- **Persistent Storage**: Login state persists across browser sessions
- **Auto-Validation**: Extension validates session on startup
- **Header Integration**: Session data used for backend API requests
- **Project Context**: Multi-project support with project selection

### 3. Logout Process
- **Data Clearing**: All stored authentication data is cleared
- **UI Reset**: Extension UI returns to login state
- **Session Invalidation**: Backend session is invalidated

## Data Structures

### Login Data Schema
```typescript
interface DrCodeLoginData {
  sessionId: string;                    // Unique session identifier
  userData: {
    user_id: string;                    // User's unique ID
    username: string;                   // Username
    name: string;                       // Display name
    carrier: string;                    // User's carrier/organization type
    orgs: string[];                     // Associated organizations
  };
  isLoggedIn: boolean;                  // Login status flag
  lastLoginTime?: number;               // Timestamp of last login
  projects?: Project[];                 // Available projects
  selectedProjectId?: number;           // Currently selected project
}

interface Project {
  id: number;                           // Project ID
  name: string;                         // Project name
  description?: string;                 // Project description
  created_at: string;                   // Creation timestamp
  updated_at: string;                   // Last update timestamp
}
```

## Implementation Details

### LoginComponent (`pages/side-panel/src/components/LoginComponent.tsx`)
**Purpose**: React component providing login UI and user interaction

**Key Features**:
- **Responsive UI**: Adapts to different contexts (side panel, options page)
- **Loading States**: Visual feedback during authentication process
- **Error Handling**: Graceful handling of authentication failures
- **Theme Support**: Dark/light theme compatibility

**Methods**:
- `handleLogin()`: Initiates login process by opening DrCode login page
- `monitorLoginTab()`: Monitors login tab for completion
- `handleLogout()`: Logs out user and clears session data
- `checkLoginStatus()`: Validates current login state

### LoginService (`chrome-extension/src/background/services/loginService.ts`)
**Purpose**: Background service handling authentication logic

**Key Features**:
- **Singleton Pattern**: Single instance across extension lifecycle
- **Tab Management**: Opens and monitors login tabs
- **Data Extraction**: Extracts session data from web platform
- **Storage Integration**: Interfaces with Chrome storage APIs

**Methods**:
- `openLoginPage()`: Opens DrCode login page in new tab
- `extractLoginData()`: Extracts session data from localStorage
- `storeLoginData()`: Stores authentication data in extension storage
- `monitorLoginCompletion()`: Monitors tab for login completion
- `isLoggedIn()`: Checks current authentication status
- `logout()`: Clears authentication data

### LoginStore (`packages/storage/lib/profile/login.ts`)
**Purpose**: Type-safe storage layer for authentication data

**Key Features**:
- **Type Safety**: Full TypeScript integration
- **Live Updates**: Real-time synchronization across extension components
- **Project Management**: Multi-project support and selection
- **Header Generation**: Generates headers for backend API requests

**Methods**:
- `setLoginData()`: Stores user authentication data
- `getLoginData()`: Retrieves stored authentication data
- `isLoggedIn()`: Checks authentication status
- `setProjects()`: Stores available projects
- `setSelectedProject()`: Sets currently selected project
- `getHeaders()`: Generates headers for API requests
- `clearLoginData()`: Clears all authentication data

## Security Considerations

### 1. Data Protection
- **Local Storage**: Sensitive data stored locally in Chrome extension storage
- **No Plain Text Passwords**: Extension never handles or stores passwords
- **Session-Based**: Uses session tokens instead of credentials
- **Automatic Cleanup**: Data cleared on logout

### 2. Cross-Origin Security
- **Content Script Injection**: Secure script injection for data extraction
- **Permission Model**: Minimal required permissions
- **Tab Isolation**: Login process isolated in separate tab

### 3. Session Validation
- **Timeout Handling**: Automatic session timeout detection
- **Validation Checks**: Regular validation of session validity
- **Error Recovery**: Graceful handling of invalid sessions

## Integration Points

### 1. Backend API Integration
```typescript
// Headers automatically added to backend requests
const headers = await loginStore.getHeaders();
// Results in:
{
  'x-user-id': 'user-123',
  'x-session-id': 'session-456',
  'x-project-id': '789'
}
```

### 2. UI State Management
```typescript
// Check login status across components
const isLoggedIn = await loginStore.isLoggedIn();
const userData = await loginStore.getUserData();
```

### 3. Project Context
```typescript
// Project selection for multi-tenant support
const projects = await loginStore.getProjects();
await loginStore.setSelectedProject(projectId);
const selectedProject = await loginStore.getSelectedProject();
```

## Usage Examples

### Basic Authentication Check
```typescript
import { loginStore } from '@extension/storage';

// Check if user is logged in
const isLoggedIn = await loginStore.isLoggedIn();

if (isLoggedIn) {
  const userData = await loginStore.getUserData();
  console.log(`Welcome, ${userData.name}!`);
} else {
  // Show login UI
}
```

### Project Management
```typescript
// Get available projects
const projects = await loginStore.getProjects();

// Set selected project
await loginStore.setSelectedProject(projects[0].id);

// Get current project
const currentProject = await loginStore.getSelectedProject();
```

### API Request Headers
```typescript
// Get headers for backend requests
const headers = await loginStore.getHeaders();

// Use in API calls
const response = await fetch('/api/v1/some-endpoint', {
  headers: {
    'Content-Type': 'application/json',
    ...headers
  }
});
```

## Error Handling

### Common Error Scenarios
1. **Login Timeout**: User doesn't complete login within timeout period
2. **Tab Closure**: User closes login tab before completion
3. **Network Issues**: Connection problems during authentication
4. **Invalid Session**: Session expires or becomes invalid

### Error Recovery
- **Automatic Retry**: Retry mechanisms for transient failures
- **User Feedback**: Clear error messages and recovery instructions
- **Graceful Degradation**: Extension continues to work with limited functionality
- **Session Refresh**: Automatic session refresh when possible

## Development & Testing

### Testing Authentication Flow
1. **Mock Login**: Use development environment for testing
2. **Session Simulation**: Simulate different session states
3. **Error Scenarios**: Test various error conditions
4. **Cross-Component**: Verify state synchronization across components

### Debugging
- **Console Logging**: Comprehensive logging for authentication events
- **Storage Inspection**: Chrome DevTools for storage inspection
- **Network Monitoring**: Monitor authentication-related network requests

This authentication system provides a secure, user-friendly way to integrate the DrCode extension with the web platform while maintaining proper session management and project context throughout the user's workflow.
