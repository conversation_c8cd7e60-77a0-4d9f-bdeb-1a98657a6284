# @extension/storage

## Overview
This package provides a **type-safe abstraction layer** for Chrome extension storage APIs. It manages persistent data storage for the DrCode: UI Testing extension, including user settings, chat history, prompts, profile information, AI model configurations, and automation data.

## Architecture
- **Type-Safe Storage**: Full TypeScript integration with storage operations
- **Chrome Storage API**: Wraps chrome.storage with improved developer experience
- **Live Updates**: Real-time synchronization between extension components
- **Structured Data**: Organized storage schemas for different data types
- **Async Operations**: Promise-based API for all storage operations
- **Fallback Support**: Graceful handling when Chrome APIs are unavailable

## Key Features

### 🔒 Type Safety
- **Full TypeScript Integration**: All storage operations are fully typed
- **Schema Validation**: Structured data with proper type checking
- **Compile-Time Safety**: Catch storage errors at development time

### ⚡ Live Updates
- **Real-Time Sync**: Changes propagate instantly across all extension components
- **Event-Driven**: Reactive updates using Chrome storage change events
- **Cross-Context**: Sync between background, content scripts, and UI pages

### 🏗️ Modular Design
- **Organized Categories**: Logical separation of different data types
- **Reusable Base**: Common storage patterns abstracted into base classes
- **Extensible**: Easy to add new storage categories and types

## Storage Categories

### `/lib/base` - Core Storage Infrastructure
**Purpose**: Foundation layer providing base storage functionality

**Key Components**:
- **`base.ts`** - Core storage creation and management
- **`types.ts`** - TypeScript interfaces and type definitions
- **`enums.ts`** - Storage area enums (Local, Sync, Session, Managed)

**Features**:
- **Storage Areas**: Support for Local, Sync, Session, and Managed storage
- **Live Updates**: Real-time synchronization across extension components
- **Serialization**: Custom serialization/deserialization support
- **Error Handling**: Robust error handling and fallback mechanisms
- **Permission Checking**: Automatic storage permission validation

### `/lib/chat` - Chat History Management
**Purpose**: Manages AI chat conversations and message history

**Key Components**:
- **`history.ts`** - Chat session and message management
- **`types.ts`** - Chat-related type definitions

**Features**:
- **Session Management**: Create, update, and delete chat sessions
- **Message Storage**: Persistent message history with metadata
- **Agent Step History**: Store detailed AI agent execution steps
- **Search & Filter**: Find conversations and messages
- **Metadata Tracking**: Session timestamps, message counts, and more

**Data Structures**:
```typescript
interface ChatSession {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  messages: ChatMessage[];
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}
```

### `/lib/profile` - User Profile & Authentication
**Purpose**: Manages user authentication, profile data, and login state

**Key Components**:
- **`login.ts`** - User authentication and session management

**Features**:
- **Login State**: Track user authentication status
- **Session Management**: Handle login sessions and tokens
- **User Data**: Store user profile information
- **Organization Support**: Multi-organization user support

**Data Structure**:
```typescript
interface DrCodeLoginData {
  sessionId: string;
  userData: {
    user_id: string;
    username: string;
    name: string;
    carrier: string;
    orgs: string[];
  };
  isLoggedIn: boolean;
}
```

### `/lib/prompt` - Prompt & Template Management
**Purpose**: Manages favorite prompts and testing templates

**Key Components**:
- **`favorites.ts`** - Favorite prompt storage and management

**Features**:
- **Favorite Prompts**: Save and organize frequently used prompts
- **Template System**: Reusable prompt templates
- **Categorization**: Organize prompts by category or type
- **Import/Export**: Share prompts between users

### `/lib/settings` - Extension Configuration
**Purpose**: Comprehensive extension settings and AI model configuration

**Key Components**:
- **`generalSettings.ts`** - Core extension settings
- **`agentModels.ts`** - AI model configurations per agent
- **`llmProvider.ts`** - AI provider settings and API keys
- **`firewall.ts`** - Security and access control settings

**Features**:
- **General Settings**: Core extension behavior configuration
- **AI Model Management**: Configure different models for different agents
- **Provider Configuration**: Manage multiple AI providers (OpenAI, Anthropic, Gemini, etc.)
- **Security Settings**: Firewall and access control configuration

**Configuration Examples**:
```typescript
// General Settings
interface GeneralSettingsConfig {
  maxSteps: number;
  maxActionsPerStep: number;
  maxFailures: number;
  useVision: boolean;
  displayHighlights: boolean;
  backendUrl: string;
}

// AI Model Configuration
interface ModelConfig {
  provider: ProviderTypeEnum;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  temperature?: number;
  maxTokens?: number;
}
```

### `/lib/playwright` - Automation Script Storage
**Purpose**: Manages Playwright script generation and action tracking

**Key Components**:
- **`index.ts`** - Playwright-related storage management

**Features**:
- **Action Tracking**: Store recorded browser actions
- **Script Options**: Configuration for script generation
- **Session Management**: Track automation sessions
- **Multi-Language Support**: Support for various programming languages

### `/lib/services` - Service Configuration
**Purpose**: Manages service-specific configurations

**Key Components**:
- **`backendUrl.ts`** - Backend API URL configuration

**Features**:
- **Backend Configuration**: Manage backend API endpoints
- **Service Discovery**: Dynamic service endpoint management

## Usage Examples

### Basic Storage Operations
```typescript
import { createStorage, StorageEnum } from '@extension/storage';

// Create a storage instance
const userSettingsStorage = createStorage('user-settings', {
  theme: 'light',
  autoSave: true,
  debugMode: false
}, {
  storageEnum: StorageEnum.Local,
  liveUpdate: true
});

// Store data
await userSettingsStorage.set({
  theme: 'dark',
  autoSave: true,
  debugMode: false
});

// Retrieve data
const settings = await userSettingsStorage.get();

// Update with function
await userSettingsStorage.set(prevSettings => ({
  ...prevSettings,
  theme: 'dark'
}));
```

### Chat History Management
```typescript
import { chatHistoryStore } from '@extension/storage';

// Create a new chat session
const session = await chatHistoryStore.createSession('UI Testing Discussion');

// Add messages to the session
const message = await chatHistoryStore.addMessage(session.id, {
  role: 'user',
  content: 'How do I create a test for a login form?',
  timestamp: new Date().toISOString()
});

// Get all sessions
const sessions = await chatHistoryStore.getSessions();

// Get messages for a specific session
const sessionWithMessages = await chatHistoryStore.getSession(session.id);
```

### Settings Management
```typescript
import {
  generalSettingsStore,
  agentModelStore,
  llmProviderStore
} from '@extension/storage';

// Update general settings
await generalSettingsStore.updateSettings({
  maxSteps: 150,
  useVision: true,
  backendUrl: 'http://localhost:8001'
});

// Configure AI models for different agents
await agentModelStore.setAgentModel('planner', {
  provider: 'gemini',
  model: 'gemini-2.5-pro',
  temperature: 0.7
});

// Set up AI provider
await llmProviderStore.setProvider('gemini', {
  apiKey: 'your-api-key',
  baseUrl: 'https://generativelanguage.googleapis.com'
});
```

## Storage Schema

### Type Definitions
```typescript
interface UserSettings {
  theme: 'light' | 'dark';
  autoSave: boolean;
  debugMode: boolean;
  aiProvider: string;
}

interface ChatMessage {
  id: string;
  content: string;
  timestamp: number;
  role: 'user' | 'assistant';
  conversationId: string;
}

interface TestPrompt {
  id: string;
  name: string;
  template: string;
  variables: Record<string, any>;
  createdAt: number;
  updatedAt: number;
}
```

### Storage Keys
```typescript
// Organized storage key constants
export const STORAGE_KEYS = {
  SETTINGS: 'extension_settings',
  CHAT_HISTORY: 'chat_history',
  USER_PROFILE: 'user_profile',
  PROMPTS: 'test_prompts',
  TEMPLATES: 'prompt_templates'
} as const;
```

## Features

### Data Persistence
- **Local Storage**: Fast access for frequently used data
- **Sync Storage**: Cross-device synchronization for user settings
- **Session Storage**: Temporary data for current session

### Type Safety
```typescript
// Fully typed storage operations
const settings = await storage.get<UserSettings>('settings');
// TypeScript knows settings is of type UserSettings | undefined
```

### Error Handling
```typescript
try {
  await storage.set('key', data);
} catch (error) {
  if (error.code === 'QUOTA_EXCEEDED') {
    // Handle storage quota exceeded
  }
  // Handle other storage errors
}
```

### Data Migration
```typescript
// Handle storage schema migrations
const migrationManager = new StorageMigrationManager();
await migrationManager.migrate(currentVersion, targetVersion);
```

## Development

### Building
```bash
pnpm ready    # Build the storage package
```

### Testing
```bash
pnpm test     # Run storage tests
```

### Type Checking
```bash
pnpm type-check
```

## Storage Limits

### Chrome Storage Limits
- **Local Storage**: ~5MB per extension
- **Sync Storage**: ~100KB per extension, synced across devices
- **Session Storage**: RAM-limited, cleared on browser restart

### Best Practices
- Use appropriate storage area for data type
- Implement data cleanup and rotation
- Handle quota exceeded errors gracefully
- Optimize data structures for storage efficiency

## Testing

### Mock Storage
```typescript
// Test utilities for mocking storage
import { createMockStorage } from '@extension/storage/testing';

const mockStorage = createMockStorage();
// Use mockStorage in tests
```

### Storage Tests
```typescript
describe('Storage Operations', () => {
  it('should store and retrieve data', async () => {
    await storage.set('test', { value: 123 });
    const result = await storage.get('test');
    expect(result).toEqual({ value: 123 });
  });
});
```

This package ensures reliable, type-safe data persistence for all components of the DrCode UI Testing extension while providing a clean, consistent API for storage operations.
