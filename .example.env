# =============================================================================
# BACKEND ENVIRONMENT VARIABLES
# =============================================================================
# Copy this file to .env and fill in your actual values
# This file contains all environment variables used in the backend

# =============================================================================
# API KEYS (REQUIRED)
# =============================================================================

# OpenAI API Key - Used for LLM interactions
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone API Key - Used for vector database operations
PINECONE_API_KEY=your_pinecone_api_key_here

# Gemini API Key - Used for embeddings and AI model interactions
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# API URLS AND ENDPOINTS
# =============================================================================

# OpenAI API URL - Can be customized for proxy or alternative endpoints
OPENAI_API_URL=https://generativelanguage.googleapis.com/v1beta/openai

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# Pinecone Index Name - Name of the Pinecone index to use
PINECONE_INDEX_NAME=drcode

# Pinecone Environment - Your Pinecone environment (optional)
PINECONE_ENVIRONMENT=your_pinecone_environment

# =============================================================================
# LLM MODEL CONFIGURATION
# =============================================================================

# Default models for different operations
DEFAULT_PLAN_MODEL=gemini-2.5-flash
DEFAULT_NAVIGATION_MODEL=gemini-2.5-flash
DEFAULT_VALIDATION_MODEL=gemini-2.5-flash

# Model for test case generation
TESTCASE_GENERATOR_MODEL=planner

# Model for document evaluation
DOCUMENT_EVALUATION_MODEL=gemini-2.5-flash

# Model for auto refinement
AUTO_REFINEMENT_MODEL=planner

# =============================================================================
# PROMPT REFINEMENT CONFIGURATION
# =============================================================================

# Thresholds for prompt refinement
REFINEMENT_MIN_SPECIFICITY=0.8
REFINEMENT_SPECIFICITY_THRESHOLD=0.85
REFINEMENT_MIN_SIMILARITY=0.7
REFINEMENT_SEMANTIC_THRESHOLD=0.75
REFINEMENT_MIN_PROMPT_LENGTH=5

# Force auto refinement (true/false)
FORCE_AUTO_REFINEMENT=true

# Refinement limits and timeouts
REFINEMENT_MAX_ITERATIONS=3
REFINEMENT_SESSION_TIMEOUT_HOURS=24

# Vector search configuration for refinement
REFINEMENT_VECTOR_SEARCH_TOP_K=5
REFINEMENT_CONTEXT_CHUNKS=3

# Question generation for refinement
REFINEMENT_MAX_QUESTIONS=3
REFINEMENT_QUESTION_MODEL=gemini-2.5-flash
REFINEMENT_QUESTION_TEMPERATURE=0.3

# Prompt refinement model configuration
REFINEMENT_PROMPT_MODEL=gemini-2.5-flash
REFINEMENT_PROMPT_TEMPERATURE=0.1

# =============================================================================
# OPTIONAL/PROXY CONFIGURATION
# =============================================================================

# LLM Proxy API Key (if using a proxy service)
LLM_PROXY_API_KEY=your_llm_proxy_api_key

# =============================================================================
# NOTES
# =============================================================================
# 
# 1. The following variables are REQUIRED for the backend to start:
#    - OPENAI_API_KEY
#    - PINECONE_API_KEY  
#    - GEMINI_API_KEY
#
# 2. All other variables have sensible defaults and are optional
#
# 3. For Docker deployment, these variables will be automatically
#    loaded from this .env file
#
# 4. Make sure to never commit your actual .env file with real API keys
#    to version control
#
# =============================================================================