# Chrome Extension - DrCode: UI Testing

## Overview
This directory contains the **core Chrome extension configuration** for DrCode: UI Testing, an AI-powered browser automation and UI testing extension. It manages the extension manifest, build configuration, and essential settings that define how the extension operates within the browser environment to provide intelligent web page interaction and testing capabilities.

## Architecture
- **Manifest V3**: Modern Chrome extension architecture with service workers
- **Multi-Browser Support**: Chrome, Firefox, and Opera compatibility
- **TypeScript**: Type-safe development with modern JavaScript features
- **Vite Build System**: Fast development and production builds
- **AI Integration**: LangChain-based AI agents for intelligent automation
- **Modular Design**: Separated concerns with background services, content scripts, and UI pages

## Key Features

### 🤖 AI-Powered Automation
- **LangChain Integration**: Advanced AI agents for intelligent task execution
- **Multi-Model Support**: Gemini, OpenAI, Anthropic, Groq, and Ollama providers
- **Vision Capabilities**: AI can see and understand web page content
- **Natural Language Processing**: Execute complex tasks described in plain English
- **Context Awareness**: Maintains context across multi-step automation tasks

### 🎯 Browser Automation
- **DOM Manipulation**: Click, type, scroll, and interact with web elements
- **Navigation Control**: Advanced page navigation and tab management
- **Element Selection**: Intelligent element identification and interaction
- **Screenshot Capture**: Visual documentation of automation steps
- **Real-time Feedback**: Live updates during task execution

### 📝 Playwright Script Generation
- **Action Tracking**: Automatic recording of all browser interactions
- **Multi-Language Support**: Generate scripts in JavaScript, TypeScript, Python, Java, C#
- **Customizable Output**: Include comments, assertions, and wait statements
- **Re-execution**: Replay recorded actions without AI agents
- **Export Functionality**: Download generated scripts as files

### 🧪 Test Case Generation & Execution
- **AI-Generated Tests**: Create comprehensive test cases using critical thinking
- **Multiple Categories**: Security, performance, accessibility, compatibility testing
- **Real Browser Testing**: Execute tests in actual browser environment
- **Visual Evidence**: Screenshot capture and detailed execution logging
- **Test Management**: Complete CRUD operations for test cases

### 🎨 User Interface
- **Side Panel**: Primary testing interface that persists across tabs
- **Options Page**: Comprehensive settings and configuration management
- **Content Scripts**: Non-intrusive page interaction capabilities
- **Dark/Light Themes**: Customizable UI themes
- **Responsive Design**: Works across different screen sizes

## Key Files & Configuration

### `manifest.js`
**Purpose**: Dynamic manifest generation with cross-browser support

**Key Features**:
- **Dynamic Configuration**: Generates manifest based on environment variables
- **Cross-Browser Compatibility**: Handles Chrome, Firefox, and Opera differences
- **Side Panel Support**: Chrome side panel integration (disabled for Firefox)
- **Opera Sidebar**: Opera-specific sidebar action support
- **Permissions Management**: Comprehensive permission declarations

**Browser-Specific Adaptations**:
```javascript
// Chrome: Uses sidePanel API
side_panel: {
  default_path: 'side-panel/index.html'
}

// Opera: Uses sidebar_action API
sidebar_action: {
  default_panel: 'side-panel/index.html',
  default_title: 'DrCode: UI Testing'
}

// Firefox: No sidebar support (falls back to popup)
```

### `package.json`
**Purpose**: Extension-specific dependencies and build configuration

**Key Dependencies**:
- **LangChain Integrations**: Multiple AI provider support
  - `@langchain/openai`: OpenAI integration
  - `@langchain/anthropic`: Claude integration
  - `@langchain/google-genai`: Google AI integration
  - `@langchain/groq`: Groq API integration
  - `@langchain/ollama`: Local AI model support
- **Workspace Packages**: Internal package dependencies
  - `@extension/storage`: Data persistence layer
  - `@extension/shared`: Common utilities and hooks
  - `@extension/ui`: UI component library
- **JSON Repair**: Data parsing and validation utilities
- **WebExtension Polyfill**: Cross-browser compatibility

### `src/background/` - Background Service Worker
**Purpose**: Core extension logic and AI agent coordination

**Key Components**:
- **`index.ts`** - Main service worker entry point and message handling
- **`agent/`** - AI agent implementations
  - `executor.ts` - Task execution coordinator
  - `event/` - Event handling and state management
  - `helper.ts` - AI model creation and configuration
- **`browser/`** - Browser interaction services
  - `context.ts` - Browser state management
  - `dom/service.ts` - DOM manipulation utilities
- **`services/`** - Core extension services
  - `speechToText.ts` - Voice input processing
  - `playwrightGenerator.ts` - Script generation
  - `initalize.ts` - Extension initialization

### `tsconfig.json`
**Purpose**: TypeScript configuration for extension development
- Type checking configuration
- Module resolution settings
- Chrome extension type declarations
- Path mapping for workspace packages

### `vite.config.mts`
**Purpose**: Vite build configuration
- Development and production build settings
- Asset handling and optimization
- Environment-specific configurations
- Chrome extension specific bundling

## Extension Permissions

### Core Permissions
- **`storage`**: Extension storage for settings and data
- **`scripting`**: Dynamic script injection capabilities
- **`tabs`**: Tab access and manipulation
- **`activeTab`**: Current tab interaction
- **`debugger`**: Advanced debugging capabilities
- **`unlimitedStorage`**: Large data storage support

### Host Permissions
- **`<all_urls>`**: Access to all websites for testing
- **Content Script Injection**: Automatic script injection on all sites

## Extension Components

### Background Service Worker
- **File**: `background.iife.js`
- **Type**: Module-based service worker
- **Purpose**: Extension lifecycle management, API handling

### Content Scripts
- **File**: `content/index.iife.js`
- **Matches**: All HTTP/HTTPS URLs
- **Purpose**: DOM manipulation, page interaction, testing execution

### Extension Pages
- **Options Page**: `options/index.html` - Extension settings and configuration
- **Side Panel**: `side-panel/index.html` - Main testing interface
- **Popup**: Extension popup interface

### Web Accessible Resources
- JavaScript files for injected functionality
- CSS files for styling
- Icons and images
- Permission handling pages

## Build Process

### Development Build
```bash
pnpm dev  # Development build with hot reload
```

### Production Build
```bash
pnpm build  # Optimized production build
```

### Environment Variables
- **`__DEV__`**: Development mode flag
- **`__FIREFOX__`**: Firefox-specific build
- **`__OPERA__`**: Opera-specific build

## Multi-Browser Strategy

### Chrome (Primary Target)
- Full feature support including side panel
- Modern Manifest V3 APIs
- Advanced debugging capabilities

### Firefox Compatibility
- Removes unsupported features (side panel)
- Uses compatible API alternatives
- Maintains core functionality

### Opera Support
- Uses sidebar_action for Opera-specific features
- Maintains Chrome Web Store compatibility
- Enhanced sidebar experience

## AI Integration
The extension integrates with multiple AI providers through LangChain, with primary focus on Gemini:
- **Google Gemini**: Primary AI provider for test generation and analysis (gemini-2.5-pro, gemini-2.5-flash)
- **Anthropic**: Claude for code analysis
- **Groq**: Fast inference capabilities
- **Ollama**: Local AI model support
- **OpenAI**: Legacy support via Gemini proxy

This multi-provider approach ensures reliability while prioritizing Gemini for optimal performance and cost efficiency.
