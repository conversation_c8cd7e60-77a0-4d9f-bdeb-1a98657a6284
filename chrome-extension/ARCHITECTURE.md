# DrCode: UI Testing Extension Architecture

## Overview
This document provides a comprehensive overview of the DrCode: UI Testing extension architecture, detailing how the various components work together to provide AI-powered browser automation and testing capabilities.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Chrome Extension                             │
├─────────────────────────────────────────────────────────────────┤
│  Background Service Worker (Persistent)                        │
│  ├── AI Agent Executor                                         │
│  ├── Browser Context Manager                                   │
│  ├── Playwright Script Generator                               │
│  ├── Speech-to-Text Service                                    │
│  └── Message Router                                            │
├─────────────────────────────────────────────────────────────────┤
│  Content Scripts (Injected per tab)                            │
│  ├── DOM Manipulation                                          │
│  ├── Element Interaction                                       │
│  ├── Screenshot Capture                                        │
│  └── Page State Monitoring                                     │
├─────────────────────────────────────────────────────────────────┤
│  Extension Pages                                               │
│  ├── Side Panel (Primary UI)                                   │
│  ├── Options Page (Settings)                                   │
│  └── Content Pages (Specialized UIs)                           │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Backend API Server                          │
│  ├── Auto-Refinement Service                                   │
│  ├── Vector Database (Pinecone)                                │
│  ├── Test Case Generation                                      │
│  ├── Document Evaluation                                       │
│  └── AI Model Proxy (Gemini)                                   │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Background Service Worker (`src/background/`)

#### Purpose
The background service worker is the heart of the extension, running persistently to coordinate AI agents, manage browser state, and handle complex automation tasks.

#### Key Responsibilities
- **AI Agent Coordination**: Manages LangChain-based AI agents for task execution
- **Browser State Management**: Tracks and manages browser state across tabs
- **Message Routing**: Handles communication between extension components
- **Action Tracking**: Records all automation actions for script generation
- **API Integration**: Communicates with backend services and AI providers

#### Main Components

##### Agent System (`agent/`)
- **`executor.ts`**: Main task execution coordinator
  - Orchestrates multi-step automation tasks
  - Manages AI agent lifecycle and state
  - Handles error recovery and retry logic
  - Coordinates between planner, navigator, and validator agents

- **`event/`**: Event handling and state management
  - Manages execution state transitions
  - Handles user interruptions and cancellations
  - Provides real-time status updates

- **`helper.ts`**: AI model creation and configuration
  - Creates and configures LangChain models
  - Handles API key management and model switching
  - Provides model-specific optimizations

##### Browser Management (`browser/`)
- **`context.ts`**: Browser state management
  - Tracks active tabs and windows
  - Manages page navigation and state
  - Provides browser capability detection
  - Handles tab lifecycle events

- **`dom/service.ts`**: DOM manipulation utilities
  - Converts web pages to markdown for AI processing
  - Extracts page structure and content
  - Provides element selection and interaction utilities

##### Services (`services/`)
- **`playwrightGenerator.ts`**: Script generation service
  - Records browser actions automatically
  - Generates Playwright scripts in multiple languages
  - Provides script customization options
  - Handles script export and download

- **`speechToText.ts`**: Voice input processing
  - Converts speech to text for voice commands
  - Integrates with browser speech recognition APIs
  - Provides voice-controlled automation

- **`initalize.ts`**: Extension initialization
  - Sets up extension services and dependencies
  - Configures AI models and providers
  - Initializes storage and settings

### 2. Content Scripts (`pages/content/`)

#### Purpose
Content scripts run in the context of web pages, providing the interface between the extension and the DOM for direct page manipulation and interaction.

#### Key Responsibilities
- **DOM Interaction**: Direct manipulation of page elements
- **Element Selection**: Interactive element picker and selector generation
- **Test Execution**: Execute generated test cases in page context
- **Visual Feedback**: Provide visual indicators and overlays
- **Screenshot Capture**: Take screenshots for documentation

#### Features
- **Element Highlighting**: Visual indicators for selected elements
- **Interactive Selection**: Click-to-select element picker
- **Action Execution**: Perform clicks, typing, scrolling, etc.
- **State Monitoring**: Track page changes and loading states
- **Error Handling**: Capture and report execution errors

### 3. Side Panel (`pages/side-panel/`)

#### Purpose
The side panel serves as the primary user interface, providing a persistent testing dashboard that remains open while users navigate between tabs.

#### Key Features
- **Chat Interface**: Natural language interaction with AI agents
- **Test Management**: Create, manage, and execute test cases
- **History Tracking**: View and manage automation history
- **Real-time Feedback**: Live updates during task execution
- **Settings Access**: Quick access to extension configuration

#### Components
- **Chat System**: AI-powered conversation interface
- **Test Case Generator**: UI for creating and managing tests
- **Execution Monitor**: Real-time task execution status
- **History Browser**: Access to previous automation sessions
- **Quick Settings**: Frequently used configuration options

### 4. Options Page (`pages/options/`)

#### Purpose
Comprehensive configuration interface for all extension settings, AI providers, and advanced features.

#### Configuration Sections
- **General Settings**: Basic extension preferences and behavior
- **Model Settings**: AI provider configuration and API keys
- **Firewall Settings**: Security and access control
- **Vector Database**: Document storage and retrieval configuration
- **Account Management**: User authentication and profile settings

## Data Flow

### 1. Task Execution Flow
```
User Input (Side Panel) 
    ↓
Background Service Worker
    ↓
AI Agent Processing
    ↓
Browser Context Analysis
    ↓
Content Script Execution
    ↓
DOM Manipulation
    ↓
Result Feedback (Side Panel)
```

### 2. Script Generation Flow
```
User Actions (Any Page)
    ↓
Action Tracking (Background)
    ↓
Playwright Generator Service
    ↓
Script Generation
    ↓
Export/Download (Side Panel)
```

### 3. Test Case Flow
```
User Prompt (Side Panel)
    ↓
Backend API Call
    ↓
AI Test Generation
    ↓
Test Storage
    ↓
Execution Request
    ↓
Browser Automation
    ↓
Results Collection
```

## Communication Patterns

### 1. Message Passing
The extension uses Chrome's message passing API for communication between components:

- **Background ↔ Content Scripts**: Task execution and DOM manipulation
- **Background ↔ Side Panel**: User interface updates and command processing
- **Background ↔ Options Page**: Settings management and configuration
- **Content Scripts ↔ Side Panel**: Direct feedback and status updates

### 2. Storage Management
- **Chrome Storage API**: Persistent settings and configuration
- **Session Storage**: Temporary data and execution state
- **IndexedDB**: Large datasets and automation history
- **Memory Storage**: Real-time state and cache

### 3. External APIs
- **Backend API**: Test generation, vector database, auto-refinement
- **AI Providers**: OpenAI, Anthropic, Google, Groq, Ollama
- **Browser APIs**: Tabs, scripting, debugger, downloads

## Security Considerations

### 1. Permissions Model
- **Minimal Permissions**: Only request necessary permissions
- **Dynamic Permissions**: Request additional permissions as needed
- **User Consent**: Clear permission explanations and user control

### 2. Data Protection
- **Local Storage**: Sensitive data stored locally when possible
- **Encryption**: API keys and sensitive settings encrypted
- **Sandboxing**: Content scripts run in isolated environments

### 3. API Security
- **Key Management**: Secure storage and handling of API keys
- **Rate Limiting**: Prevent abuse of external APIs
- **Input Validation**: Sanitize all user inputs and external data

## Performance Optimizations

### 1. Lazy Loading
- **Dynamic Imports**: Load modules only when needed
- **Component Splitting**: Split large components for better performance
- **Resource Management**: Efficient memory and resource usage

### 2. Caching Strategies
- **Model Caching**: Cache AI model responses when appropriate
- **DOM Caching**: Cache page structure for repeated operations
- **Settings Caching**: Cache frequently accessed settings

### 3. Background Processing
- **Async Operations**: Non-blocking operations throughout
- **Worker Threads**: Offload heavy processing when possible
- **Batch Operations**: Group similar operations for efficiency

This architecture provides a robust, scalable foundation for AI-powered browser automation while maintaining security, performance, and user experience standards.
