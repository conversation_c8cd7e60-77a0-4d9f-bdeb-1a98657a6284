# DrCode: UI Testing - Documentation Index

## 📚 Complete Documentation Guide

This document provides a comprehensive index of all documentation available for the DrCode: UI Testing project. All documentation has been updated and verified for accuracy as of January 2025.

## 🏠 Main Documentation

### [README.md](README.md)
**Main project overview and getting started guide**
- Complete feature overview
- Quick start instructions
- Development workflow
- Usage examples
- Troubleshooting guide

## 🔧 Backend Documentation

### Core Backend
- **[Backend README](backend/README.md)** - Complete backend overview, setup, and API reference
- **[API Documentation](backend/API_DOCUMENTATION.md)** - Comprehensive API endpoint reference with examples
- **[Docker Setup](backend/DOCKER_README.md)** - Docker deployment and containerization guide

### Specific Features
- **[Vector Database](backend/VECTOR_DB_NEW_SCHEMA.md)** - Pinecone integration, hierarchical structure, and search capabilities
- **[Test Case Generation](backend/TESTCASE_GENERATOR_README.md)** - AI-powered test case creation and execution

### Backend Components
- **[Controllers](backend/controllers/README.md)** - FastAPI route controllers and HTTP handling
- **[Services](backend/services/README.md)** - Business logic layer and external integrations
- **[Models](backend/models/README.md)** - Pydantic data models and validation
- **[Prompt Refinement](backend/prompt_refinement/README.md)** - AI-powered prompt improvement system
- **[Vector DB Client](backend/vector_db/README.md)** - Pinecone client implementation

## 🌐 Chrome Extension Documentation

### Core Extension
- **[Chrome Extension README](chrome-extension/README.md)** - Extension configuration and features
- **[Extension Architecture](chrome-extension/ARCHITECTURE.md)** - Technical architecture and component overview
- **[Playwright Script Generation](chrome-extension/PLAYWRIGHT_README.md)** - Browser action recording and script generation

### Extension Pages
- **[Side Panel](pages/side-panel/README.md)** - Primary testing interface documentation
- **[Options Page](pages/options/README.md)** - Settings and configuration interface
- **[Content Scripts](pages/content/README.md)** - Web page interaction and DOM manipulation

## 📦 Package Documentation

### Shared Packages
- **[Storage Package](packages/storage/README.md)** - Type-safe Chrome storage abstraction with live updates
- **[UI Components](packages/ui/README.md)** - Reusable React component library
- **[Shared Utilities](packages/shared/README.md)** - Common utilities, hooks, and helper functions
- **[Development Utils](packages/dev-utils/README.md)** - Development tools and debugging utilities
- **[Internationalization](packages/i18n/README.md)** - Multi-language support and translations

## 🔐 System Documentation

### Authentication & Security
- **[Authentication System](AUTHENTICATION.md)** - Complete login system, session management, and security

### Development & Deployment
- **[Development Guide](DEVELOPMENT.md)** - Detailed development setup and workflow (if exists)
- **[Deployment Guide](DEPLOYMENT.md)** - Production deployment instructions (if exists)

## 📋 Documentation by Feature

### AI & Automation Features
| Feature | Documentation | Description |
|---------|---------------|-------------|
| AI Agents | [Extension Architecture](chrome-extension/ARCHITECTURE.md) | LangChain-based AI agents for automation |
| Auto-Refinement | [Backend README](backend/README.md) | AI-powered prompt improvement |
| Test Generation | [Test Case Generator](backend/TESTCASE_GENERATOR_README.md) | AI-generated comprehensive test cases |
| Script Generation | [Playwright README](chrome-extension/PLAYWRIGHT_README.md) | Browser action recording and script export |

### Data & Storage Features
| Feature | Documentation | Description |
|---------|---------------|-------------|
| Vector Database | [Vector DB Schema](backend/VECTOR_DB_NEW_SCHEMA.md) | Pinecone integration and document storage |
| Chrome Storage | [Storage Package](packages/storage/README.md) | Extension data persistence |
| Authentication | [Authentication System](AUTHENTICATION.md) | User login and session management |
| Settings Management | [Storage Package](packages/storage/README.md) | Configuration and preferences |

### User Interface Features
| Feature | Documentation | Description |
|---------|---------------|-------------|
| Side Panel | [Side Panel README](pages/side-panel/README.md) | Primary testing interface |
| Options Page | [Options README](pages/options/README.md) | Settings and configuration |
| UI Components | [UI Package](packages/ui/README.md) | Reusable component library |
| Themes | [UI Package](packages/ui/README.md) | Dark/light theme support |

### Browser Integration Features
| Feature | Documentation | Description |
|---------|---------------|-------------|
| Content Scripts | [Content README](pages/content/README.md) | Web page interaction |
| Extension Manifest | [Chrome Extension README](chrome-extension/README.md) | Browser permissions and configuration |
| Cross-Browser Support | [Chrome Extension README](chrome-extension/README.md) | Chrome, Firefox, Opera compatibility |

## 🚀 Quick Navigation

### For New Developers
1. Start with [Main README](README.md) for project overview
2. Follow [Backend README](backend/README.md) for backend setup
3. Review [Extension Architecture](chrome-extension/ARCHITECTURE.md) for technical understanding
4. Check [Storage Package](packages/storage/README.md) for data management

### For API Integration
1. [API Documentation](backend/API_DOCUMENTATION.md) - Complete API reference
2. [Authentication System](AUTHENTICATION.md) - Login and session management
3. [Vector Database](backend/VECTOR_DB_NEW_SCHEMA.md) - Document storage and search

### For Extension Development
1. [Chrome Extension README](chrome-extension/README.md) - Extension configuration
2. [Extension Architecture](chrome-extension/ARCHITECTURE.md) - Technical architecture
3. [Storage Package](packages/storage/README.md) - Data persistence
4. [UI Components](packages/ui/README.md) - Component library

### For Testing & Automation
1. [Test Case Generation](backend/TESTCASE_GENERATOR_README.md) - AI-powered test creation
2. [Playwright Script Generation](chrome-extension/PLAYWRIGHT_README.md) - Script recording and export
3. [Content Scripts](pages/content/README.md) - Web page interaction

## 📝 Documentation Standards

All documentation in this project follows these standards:
- **Up-to-date**: Verified accurate as of January 2025
- **Comprehensive**: Covers setup, usage, and examples
- **Consistent**: Follows the same structure and formatting
- **Practical**: Includes working code examples and usage patterns
- **Cross-referenced**: Links to related documentation

## 🔄 Documentation Maintenance

This documentation index and all referenced documents are actively maintained. When adding new features or making changes:

1. Update the relevant documentation files
2. Add new documentation to this index
3. Ensure cross-references are updated
4. Verify all examples and code snippets work
5. Update the "last updated" information

---
