# DrCode: UI Testing - Vector Database Implementation

## Overview

This document describes the comprehensive vector database architecture implemented for DrCode: UI Testing, featuring a hierarchical user → project → file → chunks structure with namespace-based isolation, Gemini embeddings, and advanced search capabilities.

## Architecture

### High-Level Design
```
┌─────────────────────────────────────────────────────────────────┐
│                    Vector Database System                      │
├─────────────────────────────────────────────────────────────────┤
│  API Layer (FastAPI Controllers)                               │
│  ├── Document Management Endpoints                             │
│  ├── File Upload & Processing                                  │
│  ├── Search & Query Endpoints                                  │
│  └── Project & File Management                                 │
├─────────────────────────────────────────────────────────────────┤
│  Service Layer                                                 │
│  ├── VectorDBService (Business Logic)                          │
│  ├── Document Processing (PDF, DOCX, TXT, MD)                  │
│  ├── Text Chunking & Preprocessing                             │
│  └── File Type Detection                                       │
├─────────────────────────────────────────────────────────────────┤
│  Vector Database Layer                                         │
│  ├── PineconeVectorDB (Client)                                 │
│  ├── Gemini Embeddings (embedding-001)                         │
│  ├── Namespace Management                                      │
│  └── Metadata Filtering                                        │
├─────────────────────────────────────────────────────────────────┤
│  External Services                                             │
│  ├── Pinecone Cloud (Vector Storage)                           │
│  ├── Google Gemini (Embeddings)                                │
│  └── File Storage Simulation                                   │
└─────────────────────────────────────────────────────────────────┘
```

### Hierarchical Data Structure
```
User (Namespace)
├── Project A
│   ├── File 1 (document.pdf)
│   │   ├── Chunk 1
│   │   ├── Chunk 2
│   │   └── Chunk N
│   └── File 2 (requirements.docx)
│       ├── Chunk 1
│       └── Chunk N
└── Project B
    └── File 3 (notes.txt)
        └── Chunks...
```

**Key Principles**:
- **Namespace Isolation**: `user_id` provides complete data isolation between users
- **Metadata Filtering**: `project_id` and `file_id` enable granular querying
- **Chunk-based Storage**: Files are intelligently split into chunks for optimal retrieval
- **Hierarchical Organization**: Clear data hierarchy for scalable management

### Vector Record Schema

Each Pinecone vector record follows this comprehensive structure:

```json
{
  "id": "file001_chunk_01",
  "values": [/* 768-dimensional Gemini embedding array */],
  "metadata": {
    "user_id": "user123",
    "project_id": "projABC",
    "file_id": "file001",
    "filename": "document.pdf",
    "file_url": "https://storage.drcode.com/user123/projABC/document.pdf",
    "chunk_index": 1,
    "chunk_text": "Some extracted text content...",
    "created_at": "2025-01-01T10:00:00Z",
    "type": "UI"
  }
}
```

**Metadata Fields**:
- **`user_id`**: User identifier for namespace isolation
- **`project_id`**: Project identifier for project-scoped queries
- **`file_id`**: Unique file identifier (UUID)
- **`filename`**: Original filename for user reference
- **`file_url`**: Simulated storage URL for file access
- **`chunk_index`**: Sequential chunk number within file
- **`chunk_text`**: Actual text content of the chunk
- **`created_at`**: ISO timestamp of chunk creation
- **`type`**: Data type identifier ("UI" for UI-related content)

## Configuration & Setup

### Environment Variables
```bash
# Pinecone Configuration
PINECONE_INDEX_NAME=drcode
PINECONE_API_KEY=your_pinecone_api_key

# Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key

# Optional: Custom configurations
VECTOR_DB_CHUNK_SIZE=1000
VECTOR_DB_CHUNK_OVERLAP=200
```

### Pinecone Index Configuration
- **Index Name**: `drcode` (configurable via environment)
- **Dimension**: 768 (Gemini embedding-001 model)
- **Metric**: cosine similarity
- **Cloud Provider**: AWS (recommended)
- **Region**: us-east-1 (or closest to your deployment)
- **Namespaces**: Used for complete user data isolation

### Gemini Embedding Configuration
- **Model**: `models/embedding-001`
- **Task Type**: `retrieval_document`
- **Dimension**: 768 dimensions
- **Context Length**: Up to 2048 tokens per chunk
- **Rate Limits**: Handled automatically with retry logic

## API Endpoints

### Document Management

#### 1. File Upload with Context
Upload files with user and project context for organized storage.

```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

file: [file]                    # Required: File to upload (PDF, DOCX, TXT, MD)
user_id: demo_user             # Optional: User identifier (defaults to "demo_user")
project_id: demo_project       # Optional: Project identifier (defaults to "demo_project")
```

**Response:**
```json
{
  "success": true,
  "message": "File processed and 5 chunks indexed successfully",
  "file_id": "uuid-generated-id",
  "filename": "document.pdf",
  "file_url": "https://storage.drcode.com/demo_user/demo_project/document.pdf",
  "chunks_added": 5,
  "user_id": "demo_user",
  "project_id": "demo_project"
}
```

#### 2. List User Files
Get all files for a user or specific project.

```http
GET /api/v1/files/list?user_id=demo_user&project_id=demo_project
```

**Response:**
```json
{
  "success": true,
  "files": [
    {
      "file_id": "file001",
      "filename": "document.pdf",
      "file_url": "https://storage.drcode.com/demo_user/demo_project/document.pdf",
      "chunks_count": 5,
      "created_at": "2025-01-01T10:00:00Z",
      "project_id": "demo_project"
    }
  ],
  "total_files": 1
}
```

#### 3. Delete File
Remove a file and all its associated chunks.

```http
DELETE /api/v1/files/{file_id}?user_id=demo_user&project_id=demo_project
```

### Search & Query

#### 4. Query by Project
Search across all files within a specific project.

```http
POST /api/v1/query/project
Content-Type: application/json

{
  "query": "user authentication methods",
  "user_id": "demo_user",
  "project_id": "demo_project",
  "top_k": 5
}
```

**Response:**
```json
{
  "success": true,
  "message": "Found 3 results in project demo_project",
  "results": [
    {
      "id": "file001_chunk_01",
      "score": 0.95,
      "text": "User authentication can be implemented using various methods...",
      "metadata": {
        "user_id": "demo_user",
        "project_id": "demo_project",
        "file_id": "file001",
        "filename": "auth_guide.pdf",
        "chunk_index": 1,
        "created_at": "2025-01-01T10:00:00Z",
        "type": "UI"
      }
    }
  ],
  "total_results": 3
}
```

#### 5. Query by File
Search within a specific file for targeted results.

```http
POST /api/v1/query/file
Content-Type: application/json

{
  "query": "password validation rules",
  "file_id": "file-uuid",
  "user_id": "demo_user",
  "project_id": "demo_project",
  "top_k": 3
}
```

### Legacy Endpoints (Backward Compatibility)

#### 6. Document Upload (Legacy)
```http
POST /api/v1/documents/upload
Content-Type: multipart/form-data

file: [file]
```

#### 7. Search Documents (Legacy)
```http
POST /api/v1/search
Content-Type: application/json

{
  "query": "search text",
  "top_k": 5
}
```

## Supported File Types

- **Text Files**: `.txt`, `.md`
- **Documents**: `.docx` (requires python-docx)
- **PDFs**: `.pdf` (requires PyPDF2)

## Key Features

### 1. Namespace Isolation
- Each user has their own namespace in Pinecone
- Complete data isolation between users
- No cross-user data leakage

### 2. Hierarchical Filtering
- Query all files in a project
- Query specific files within a project
- Metadata-based filtering for precise results

### 3. Intelligent Chunking
- Automatic text chunking with overlap
- Sentence and word boundary preservation
- Configurable chunk size and overlap

### 4. File Storage Simulation
- Generates file URLs for storage reference
- Maintains file metadata and relationships
- Ready for integration with actual file storage

## Implementation Details

### Core Classes

#### PineconeVectorDB
- `index_file_chunk()`: Index individual file chunks
- `query_by_project()`: Search within project scope
- `query_by_file()`: Search within specific file
- Uses Gemini embeddings for vector generation

#### VectorDBService
- `upload_file_with_context()`: Process and index files
- `query_project_documents()`: Project-wide search
- `query_file_documents()`: File-specific search
- Handles file type detection and text extraction

### Frontend Components

#### DocumentRAGModal
- File upload with drag-and-drop
- Project and file-specific querying
- Real-time search results display
- Support for multiple file types

## Demo Configuration

For development and testing, the system uses dummy values:
- **User ID**: `demo_user`
- **Project ID**: `demo_project`

These will be replaced with actual authentication values in production.

## Testing

Run the test script to validate functionality:

```bash
cd backend
python test_vector_db_new_schema.py
```

The test covers:
- File upload and chunking
- Project-wide queries
- File-specific queries
- Namespace isolation
- Text chunking functionality

## Migration from Old Schema

The new implementation maintains backward compatibility with existing endpoints:
- `/api/v1/documents/upload` (legacy)
- `/api/v1/search` (legacy)

New endpoints provide enhanced functionality:
- `/api/v1/files/upload` (with context)
- `/api/v1/query/project` (project-scoped)
- `/api/v1/query/file` (file-scoped)

## Future Enhancements

1. **Authentication Integration**: Replace dummy user/project IDs
2. **File Storage**: Implement actual file storage service
3. **Advanced Filtering**: Add date ranges, file types, etc.
4. **Batch Operations**: Support multiple file uploads
5. **Analytics**: Track usage and performance metrics
