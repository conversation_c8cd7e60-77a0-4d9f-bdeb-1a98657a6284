# Docker Setup for DrCode: UI Testing Backend

This directory contains Docker configuration for running the DrCode: UI Testing backend API server.

## Files

- `Dockerfile` - Multi-stage Docker build configuration
- `.dockerignore` - Files to exclude from Docker build context
- `docker-compose.yml` - Docker Compose configuration for easy deployment

## Prerequisites

1. <PERSON><PERSON> and <PERSON><PERSON> Compose installed
2. Required environment variables set (see `.env.example`)

## Environment Variables

Create a `.env` file with the following variables:

```bash
# Primary AI Provider
GEMINI_API_KEY=your_gemini_api_key

# Vector Database
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=drcode

# OpenAI Compatibility Layer (maps to Gemini)
OPENAI_API_KEY=your_gemini_api_key
OPENAI_API_URL=https://generativelanguage.googleapis.com/v1beta/openai
```

## Quick Start

### Using Docker Compose (Recommended)

```bash
# Build and start the service
docker-compose up --build

# Run in background
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop the service
docker-compose down
```

### Using Docker directly

```bash
# Build the image
docker build -t drcode-backend .

# Run the container
docker run -p 8001:8001 \
  -e GEMINI_API_KEY=your_key \
  -e PINECONE_API_KEY=your_key \
  -e OPENAI_API_KEY=your_gemini_key \
  -v $(pwd)/screenshots:/app/screenshots \
  -v $(pwd)/testcases:/app/testcases \
  -v $(pwd)/.env:/app/.env:ro \
  drcode-backend
```

## Access

Once running, the API will be available at:
- API Server: http://localhost:8001
- API Documentation: http://localhost:8001/docs
- Health Check: http://localhost:8001/api/v1/health

## Development

For development with auto-reload:

```bash
# Using docker-compose
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Or modify the compose file to add reload flag
# Then rebuild and run
```

## Data Persistence

The following directories are mounted as volumes:
- `./screenshots` - Screenshot storage
- `./testcases` - Test case files
- `./vector_db` - Vector database files

## Troubleshooting

1. **Port already in use**: Change the port mapping in docker-compose.yml
2. **Environment variables not loaded**: Ensure .env file exists and has correct format
3. **Build fails**: Check that all dependencies in requirements.txt are available
4. **Health check fails**: Verify the /health endpoint is working

## Production Deployment

For production:

1. Use environment variables instead of .env file
2. Configure proper logging
3. Set up reverse proxy (nginx/traefik)
4. Use proper secrets management
5. Configure monitoring and alerting
