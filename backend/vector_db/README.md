# Vector Database Client

## Overview
This directory contains the **Pinecone vector database client** for DrCode: UI Testing backend. It implements the hierarchical user → project → file → chunks structure with Gemini embeddings for semantic search and document retrieval.

## Architecture
- **Pinecone Integration**: Production-ready vector database with namespace isolation
- **Gemini Embeddings**: Google's embedding-001 model for 768-dimensional vectors
- **Hierarchical Structure**: User → Project → File → Chunks organization
- **Namespace Isolation**: Complete user data separation using Pinecone namespaces
- **Metadata Filtering**: Granular querying by project and file

## Components

### `pinecone_client.py`
**Purpose**: Core Pinecone vector database client with Gemini embeddings

**Key Features**:
- **Connection Management**: Handles Pinecone API connections and index management
- **Gemini Integration**: Uses Google's embedding-001 model for vector generation
- **Namespace Support**: Complete user isolation using Pinecone namespaces
- **Hierarchical Indexing**: Index file chunks with project and file context
- **Advanced Querying**: Project-scoped and file-scoped search capabilities
- **Metadata Management**: Rich metadata for document organization and filtering

**Core Operations**:
```python
# Initialize client
client = PineconeVectorDB(index_name="drcode")

# Index a file chunk
chunk_id = client.index_file_chunk(
    user_id="user123",
    project_id="project456",
    file_id="file789",
    filename="document.pdf",
    file_url="https://storage.example.com/file.pdf",
    text_chunk="Document content...",
    chunk_index=1
)

# Query by project
results = client.query_by_project(
    user_id="user123",
    project_id="project456",
    query_text="search query",
    top_k=5
)

# Query by specific file
results = client.query_by_file(
    user_id="user123",
    project_id="project456",
    file_id="file789",
    query_text="search query",
    top_k=3
)
```

## Vector Database Schema

### Current Implementation
**Index**: `drcode` (configurable via PINECONE_INDEX_NAME)
**Dimensions**: 768 (Gemini embedding-001)
**Metric**: Cosine similarity
**Namespaces**: User-based isolation

### Vector Record Structure
```json
{
  "id": "file001_chunk_01",
  "values": [/* 768-dimensional Gemini embedding */],
  "metadata": {
    "user_id": "user123",
    "project_id": "project456",
    "file_id": "file001",
    "filename": "document.pdf",
    "file_url": "https://storage.example.com/document.pdf",
    "chunk_index": 1,
    "chunk_text": "Actual text content of the chunk...",
    "created_at": "2025-01-01T10:00:00Z",
    "type": "UI"
  }
}
```

### Document Vectors
**Index**: `document-embeddings`
**Dimensions**: 1536 (OpenAI ada-002)
**Metadata**:
- `document_id`: Unique document identifier
- `document_type`: File type (pdf, docx, txt)
- `content_snippet`: Text excerpt
- `evaluation_score`: Quality assessment score
- `tags`: Document categorization tags

## Use Cases

### 1. Prompt Refinement
- Store historical prompts as vectors
- Find similar prompts for context
- Suggest improvements based on successful patterns
- Track prompt evolution and quality

### 2. Document Analysis
- Embed document content for similarity search
- Compare documents for duplicate detection
- Categorize documents by content similarity
- Enable semantic document retrieval

### 3. Knowledge Base
- Build searchable knowledge repository
- Store testing patterns and solutions
- Enable contextual help and suggestions
- Support AI-powered recommendations

## Configuration

### Environment Variables
```bash
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=drcode-vectors
```

### Index Configuration
```python
index_config = {
    "dimension": 1536,
    "metric": "cosine",
    "pod_type": "p1.x1",
    "replicas": 1
}
```

## Operations

### Vector Generation
```python
# Generate embeddings using OpenAI
embedding = openai.Embedding.create(
    input=text,
    model="text-embedding-ada-002"
)
```

### Similarity Search
```python
# Search for similar vectors
query_results = index.query(
    vector=query_embedding,
    top_k=10,
    include_metadata=True,
    filter={"domain": "web_testing"}
)
```

### Batch Operations
```python
# Bulk upsert for efficiency
vectors_to_upsert = [
    (id, embedding, metadata)
    for id, embedding, metadata in batch_data
]
index.upsert(vectors=vectors_to_upsert)
```

## Performance Optimization
- **Batch Processing**: Group operations for efficiency
- **Metadata Filtering**: Use filters to reduce search space
- **Index Tuning**: Optimize for query patterns
- **Caching**: Cache frequent queries and results
