# DrCode: UI Testing Backend API Documentation

## Overview
This document provides comprehensive API documentation for the DrCode: UI Testing backend server. All endpoints use JSON for request/response bodies unless otherwise specified.

**Base URL**: `http://localhost:8001`  
**API Version**: v1  
**Content-Type**: `application/json` (unless file upload)

## Authentication
Currently, the API uses demo user credentials for development. Production implementation will include proper authentication.

## Health & Monitoring Endpoints

### GET /api/v1/health
Get service health status and uptime information.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "uptime": "2:30:45"
}
```

### GET /api/v1/ping
Simple connectivity check endpoint.

**Response:**
```json
{
  "message": "pong",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### GET /api/v1/token-usage
Get AI model token usage statistics.

**Response:**
```json
{
  "total_requests": 150,
  "total_tokens": 45000,
  "total_cost": 0.45,
  "by_model": {
    "gemini-2.5-flash": {
      "requests": 100,
      "input_tokens": 20000,
      "output_tokens": 15000,
      "cost": 0.30
    }
  },
  "by_endpoint": {
    "/api/v1/auto-refine": {
      "requests": 50,
      "tokens": 15000,
      "cost": 0.15
    }
  }
}
```

### DELETE /api/v1/token-usage
Clear all token usage statistics.

**Response:**
```json
{
  "message": "Token usage data cleared successfully",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Auto-Refinement Endpoints

### POST /api/v1/auto-refine
Automatically refine a prompt using AI analysis and vector database context.

**Request Body:**
```json
{
  "prompt": "Create a login form"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Auto-refinement completed successfully",
  "original_prompt": "Create a login form",
  "final_prompt": "Create a responsive login form with username/email field, password field with show/hide toggle, remember me checkbox, forgot password link, and proper validation with error messages. Include accessibility features and mobile-friendly design.",
  "iterations_completed": 1,
  "max_iterations": 1,
  "final_specificity_score": 8.5,
  "final_semantic_score": 9.2,
  "thresholds_met": true,
  "iteration_logs": [
    {
      "iteration": 1,
      "questions": [
        "What type of authentication should the form support?",
        "Should it include social login options?",
        "What validation rules should be applied?"
      ],
      "answers": [
        "Username/email and password authentication",
        "No social login required for basic form",
        "Email format validation and password strength requirements"
      ],
      "specificity_score": 8.5,
      "semantic_score": 9.2,
      "thresholds_met": true,
      "refined_prompt": "Create a responsive login form..."
    }
  ],
  "final_analysis": {
    "improvements_made": [
      "Added specific field requirements",
      "Included accessibility considerations",
      "Specified responsive design needs"
    ],
    "context_used": true,
    "vector_matches": 3
  }
}
```

### GET /api/v1/auto-refine/health
Check auto-refinement service health.

**Response:**
```json
{
  "status": "healthy",
  "vector_db_available": true,
  "service_ready": true
}
```

## Vector Database Endpoints

### POST /api/v1/files/upload
Upload a file with user and project context for vector storage.

**Request:** `multipart/form-data`
- `file`: File to upload (PDF, DOCX, TXT, MD)
- `user_id`: User identifier (optional, defaults to "demo_user")
- `project_id`: Project identifier (optional, defaults to "demo_project")

**Response:**
```json
{
  "success": true,
  "message": "File processed and 5 chunks indexed successfully",
  "file_id": "uuid-generated-id",
  "filename": "document.pdf",
  "file_url": "https://storage.drcode.com/demo_user/demo_project/document.pdf",
  "chunks_added": 5,
  "user_id": "demo_user",
  "project_id": "demo_project"
}
```

### POST /api/v1/query/project
Search documents within a specific project.

**Request Body:**
```json
{
  "query": "user authentication methods",
  "user_id": "demo_user",
  "project_id": "demo_project",
  "top_k": 5
}
```

**Response:**
```json
{
  "success": true,
  "message": "Found 3 results in project demo_project",
  "results": [
    {
      "id": "file001_chunk_01",
      "score": 0.95,
      "text": "User authentication can be implemented using various methods including username/password, OAuth, and multi-factor authentication...",
      "metadata": {
        "user_id": "demo_user",
        "project_id": "demo_project",
        "file_id": "file001",
        "filename": "auth_guide.pdf",
        "chunk_index": 1,
        "created_at": "2024-01-01T10:00:00Z",
        "type": "UI"
      }
    }
  ],
  "total_results": 3
}
```

### POST /api/v1/query/file
Search within a specific file.

**Request Body:**
```json
{
  "query": "password validation rules",
  "file_id": "file001",
  "user_id": "demo_user",
  "project_id": "demo_project",
  "top_k": 3
}
```

### GET /api/v1/files/list
List files for a user/project.

**Query Parameters:**
- `user_id`: User identifier
- `project_id`: Project identifier (optional)

**Response:**
```json
{
  "success": true,
  "files": [
    {
      "file_id": "file001",
      "filename": "auth_guide.pdf",
      "file_url": "https://storage.drcode.com/demo_user/demo_project/auth_guide.pdf",
      "chunks_count": 5,
      "created_at": "2024-01-01T10:00:00Z",
      "project_id": "demo_project"
    }
  ],
  "total_files": 1
}
```

### DELETE /api/v1/files/{file_id}
Delete a file and all its chunks.

**Path Parameters:**
- `file_id`: File identifier to delete

**Query Parameters:**
- `user_id`: User identifier
- `project_id`: Project identifier

**Response:**
```json
{
  "success": true,
  "message": "File and 5 chunks deleted successfully",
  "file_id": "file001"
}
```

## Test Case Management Endpoints

### POST /api/v1/testcases/generate
Generate AI-powered test cases for UI testing.

**Request Body:**
```json
{
  "user_prompt": "A login form with username, password, and remember me checkbox",
  "generate_count": 5
}
```

**Response:**
```json
{
  "success": true,
  "message": "Generated 5 test cases successfully",
  "test_cases": [
    {
      "id": "test_20240101_120000_1",
      "name": "Input Validation Stress Test",
      "description": "Test login form with malicious and edge case inputs",
      "category": "security",
      "severity": "critical",
      "steps": [
        "Navigate to login page",
        "Input extremely long strings (>10000 characters) in username field",
        "Input special characters and HTML/JS code in password field",
        "Attempt to submit form",
        "Verify proper error handling and no XSS execution"
      ],
      "expected_result": "Form should handle invalid inputs gracefully without breaking or executing malicious code",
      "breaking_scenario": "Unvalidated inputs could cause XSS attacks, UI layout breaks, or application crashes",
      "status": "pending",
      "created_at": "2024-01-01T12:00:00Z"
    }
  ],
  "generation_time": "2.3s",
  "session_id": "session_20240101_120000"
}
```

### GET /api/v1/testcases/
List all test cases with optional filtering.

**Query Parameters:**
- `status`: Filter by status (pending, running, passed, failed, error)
- `category`: Filter by category (security, performance, accessibility, etc.)
- `limit`: Number of results to return (default: 50)
- `offset`: Number of results to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "test_cases": [
    {
      "id": "test_20240101_120000_1",
      "name": "Input Validation Stress Test",
      "category": "security",
      "severity": "critical",
      "status": "passed",
      "created_at": "2024-01-01T12:00:00Z",
      "executed_at": "2024-01-01T12:05:00Z",
      "execution_time": "15.2s"
    }
  ],
  "total_count": 25,
  "filtered_count": 1
}
```

### GET /api/v1/testcases/{test_id}
Get detailed information about a specific test case.

**Path Parameters:**
- `test_id`: Test case identifier

**Response:**
```json
{
  "success": true,
  "test_case": {
    "id": "test_20240101_120000_1",
    "name": "Input Validation Stress Test",
    "description": "Test login form with malicious and edge case inputs",
    "category": "security",
    "severity": "critical",
    "steps": [
      "Navigate to login page",
      "Input extremely long strings (>10000 characters) in username field",
      "Input special characters and HTML/JS code in password field",
      "Attempt to submit form",
      "Verify proper error handling and no XSS execution"
    ],
    "expected_result": "Form should handle invalid inputs gracefully",
    "breaking_scenario": "Unvalidated inputs could cause XSS attacks",
    "status": "passed",
    "created_at": "2024-01-01T12:00:00Z",
    "executed_at": "2024-01-01T12:05:00Z",
    "execution_time": "15.2s",
    "execution_log": [
      "2024-01-01T12:05:00Z - Starting test execution",
      "2024-01-01T12:05:02Z - Navigated to login page",
      "2024-01-01T12:05:05Z - Entered test data in username field",
      "2024-01-01T12:05:08Z - Entered test data in password field",
      "2024-01-01T12:05:10Z - Submitted form",
      "2024-01-01T12:05:15Z - Verified error handling - PASSED"
    ],
    "screenshots": [
      "/screenshots/test_20240101_120000_1_step_1.png",
      "/screenshots/test_20240101_120000_1_step_5.png"
    ],
    "error_message": null
  }
}
```

### POST /api/v1/testcases/execute
Execute multiple test cases.

**Request Body:**
```json
{
  "test_ids": ["test_20240101_120000_1", "test_20240101_120000_2"],
  "target_url": "https://example.com/login",
  "execution_options": {
    "take_screenshots": true,
    "stop_on_failure": false,
    "timeout_per_test": 300
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test execution started for 2 test cases",
  "execution_id": "exec_20240101_120500",
  "test_ids": ["test_20240101_120000_1", "test_20240101_120000_2"],
  "estimated_duration": "10-15 minutes"
}
```

### POST /api/v1/testcases/run-single/{test_id}
Execute a single test case.

**Path Parameters:**
- `test_id`: Test case identifier

**Request Body:**
```json
{
  "target_url": "https://example.com/login",
  "execution_options": {
    "take_screenshots": true,
    "timeout": 300
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test case executed successfully",
  "test_id": "test_20240101_120000_1",
  "status": "passed",
  "execution_time": "15.2s",
  "screenshots": [
    "/screenshots/test_20240101_120000_1_step_1.png"
  ]
}
```

### PUT /api/v1/testcases/status
Update test case execution status.

**Request Body:**
```json
{
  "test_id": "test_20240101_120000_1",
  "status": "running",
  "execution_log": "Starting test execution...",
  "error_message": null
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test case status updated successfully",
  "test_id": "test_20240101_120000_1",
  "status": "running"
}
```

### GET /api/v1/testcases/summary/stats
Get test execution statistics and summary.

**Response:**
```json
{
  "success": true,
  "stats": {
    "total_tests": 50,
    "passed": 35,
    "failed": 10,
    "error": 2,
    "pending": 3,
    "pass_rate": 70.0,
    "average_execution_time": "12.5s",
    "by_category": {
      "security": {"total": 15, "passed": 12, "failed": 3},
      "performance": {"total": 10, "passed": 8, "failed": 2},
      "accessibility": {"total": 12, "passed": 10, "failed": 2},
      "functional": {"total": 13, "passed": 5, "failed": 3, "error": 2, "pending": 3}
    },
    "by_severity": {
      "critical": {"total": 8, "passed": 5, "failed": 3},
      "high": {"total": 15, "passed": 12, "failed": 3},
      "medium": {"total": 20, "passed": 15, "failed": 3, "error": 2},
      "low": {"total": 7, "passed": 3, "failed": 1, "pending": 3}
    },
    "recent_executions": [
      {
        "execution_id": "exec_20240101_120500",
        "executed_at": "2024-01-01T12:05:00Z",
        "test_count": 5,
        "passed": 4,
        "failed": 1,
        "duration": "45.2s"
      }
    ]
  }
}
```

## AI Model Proxy Endpoint

### POST /chat/completions
OpenAI-compatible chat completions endpoint that proxies to Gemini models.

**Request Body:**
```json
{
  "model": "planner",
  "messages": [
    {
      "role": "user",
      "content": "Create a test plan for a login form"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 1000
}
```

**Response:**
```json
{
  "id": "chatcmpl-uuid",
  "object": "chat.completion",
  "created": 1704110400,
  "model": "gemini-2.5-flash",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Here's a comprehensive test plan for a login form:\n\n1. **Functional Testing**\n   - Valid login credentials\n   - Invalid username/password combinations\n   - Empty field validation\n\n2. **Security Testing**\n   - SQL injection attempts\n   - XSS vulnerability testing\n   - Brute force protection\n\n3. **UI/UX Testing**\n   - Responsive design validation\n   - Accessibility compliance\n   - Error message clarity"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 25,
    "completion_tokens": 150,
    "total_tokens": 175
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "detail": "Error message describing what went wrong",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common HTTP Status Codes
- `200` - Success
- `400` - Bad Request (invalid input)
- `401` - Unauthorized (authentication required)
- `404` - Not Found (resource doesn't exist)
- `422` - Unprocessable Entity (validation error)
- `500` - Internal Server Error

### Common Error Codes
- `INVALID_INPUT` - Request validation failed
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `API_KEY_MISSING` - Required API key not provided
- `VECTOR_DB_ERROR` - Vector database operation failed
- `AI_MODEL_ERROR` - AI model request failed
- `FILE_PROCESSING_ERROR` - File upload/processing failed
```
