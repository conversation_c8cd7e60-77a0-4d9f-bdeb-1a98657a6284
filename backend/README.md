# Backend - DrCode: UI Testing API Server

## Overview
The backend component is a **FastAPI-based Python server** that provides AI-powered services for the DrCode: UI Testing extension. It serves as the core API layer handling document evaluation, vector database operations, auto-refinement capabilities, testcase generation, health monitoring, and AI model proxy services.

## Architecture
- **Framework**: FastAPI with async/await support
- **Language**: Python 3.12+
- **API Design**: RESTful API with OpenAPI documentation
- **CORS**: Configured for cross-origin requests from extension
- **Logging**: Structured logging with configurable levels
- **AI Integration**: Primary integration with Google Gemini models
- **Proxy Services**: OpenAI-compatible API proxy for extension compatibility

## Key Features

### 1. Auto-Refinement Service
- **Automatic Prompt Refinement**: Uses vector database context for intelligent prompt improvement
- **AI-Powered Analysis**: Leverages Gemini models for prompt analysis and enhancement
- **Context-Aware Algorithms**: Incorporates similar prompts and best practices
- **Single-Iteration Processing**: Prevents over-refinement and reduces hallucination risk
- **Specificity & Semantic Scoring**: Quantitative assessment of prompt quality

### 2. Document Evaluation Service
- **Intelligent Document Analysis**: AI-powered content quality assessment
- **Multi-Format Support**: Handles various document types and formats
- **Scoring Algorithms**: Automated evaluation metrics and quality indicators
- **Content Extraction**: Advanced text processing and analysis capabilities

### 3. Vector Database Management
- **Pinecone Integration**: Production-ready vector storage with namespace isolation
- **Gemini Embeddings**: Uses Google's embedding-001 model for vector generation
- **Hierarchical Structure**: User → Project → File → Chunks organization
- **Similarity Search**: Advanced semantic search capabilities
- **File Processing**: Supports PDF, DOCX, TXT, and MD file formats
- **Chunking Strategy**: Intelligent text chunking with overlap for better retrieval

### 4. Testcase Generation & Execution
- **AI-Powered Test Generation**: Creates comprehensive UI test cases using critical thinking
- **Multiple Test Categories**: Security, performance, accessibility, compatibility, functional testing
- **Browser Automation**: Chrome extension integration for real browser testing
- **Screenshot Capture**: Visual evidence collection during test execution
- **Test Management**: Complete CRUD operations for test cases
- **Execution Tracking**: Status monitoring and result aggregation

### 5. Health Monitoring & Analytics
- **API Health Checks**: Service status and dependency monitoring
- **Token Usage Tracking**: Detailed analytics for AI model usage and costs
- **Performance Metrics**: Request timing and system performance monitoring
- **Error Tracking**: Comprehensive error logging and reporting

### 6. AI Model Proxy Services
- **OpenAI-Compatible API**: Proxy service for extension compatibility
- **Model Routing**: Intelligent routing to appropriate Gemini models
- **Request/Response Transformation**: Seamless API compatibility layer
- **Usage Analytics**: Token consumption tracking and cost monitoring

## Project Structure

### `/controllers` - API Route Handlers
FastAPI route controllers organized by functionality:

- **`auto_refinement_controller.py`** - Auto-refinement endpoints
  - `POST /api/v1/auto-refine` - Automatic prompt refinement
  - `GET /api/v1/auto-refine/health` - Service health check

- **`document_evaluation_controller.py`** - Document analysis endpoints
  - Document quality assessment and scoring endpoints

- **`vector_db_controller.py`** - Vector database operations
  - `POST /api/v1/documents` - Add single document
  - `POST /api/v1/documents/batch` - Batch document upload
  - `POST /api/v1/documents/upload` - File upload (legacy)
  - `POST /api/v1/files/upload` - File upload with context
  - `POST /api/v1/search` - Document search (legacy)
  - `POST /api/v1/query/project` - Project-scoped search
  - `POST /api/v1/query/file` - File-scoped search
  - `GET /api/v1/files/list` - List user files
  - `DELETE /api/v1/files/{file_id}` - Delete file

- **`testcase_controller.py`** - Test case management
  - `POST /api/v1/testcases/generate` - Generate test cases
  - `GET /api/v1/testcases/` - List all test cases
  - `GET /api/v1/testcases/{test_id}` - Get specific test case
  - `POST /api/v1/testcases/execute` - Execute multiple test cases
  - `POST /api/v1/testcases/run-single/{test_id}` - Execute single test
  - `PUT /api/v1/testcases/status` - Update test status
  - `GET /api/v1/testcases/summary/stats` - Execution statistics

- **`health_controller.py`** - System health monitoring
  - `GET /api/v1/health` - Service health status
  - `GET /api/v1/ping` - Simple ping endpoint

### `/services` - Business Logic Layer
Core service implementations handling business logic:

- **`auto_refinement_service.py`** - Auto-refinement core logic
  - Prompt analysis and improvement algorithms
  - Vector database integration for context
  - Gemini model integration for AI processing

- **`document_evaluation_service.py`** - Document analysis services
  - Content quality assessment algorithms
  - Multi-format document processing

- **`vector_db_service.py`** - Vector database operations
  - Pinecone client management
  - Document chunking and embedding
  - Search and retrieval operations

- **`testcase_service.py`** - Test case management
  - AI-powered test generation
  - Test execution coordination
  - Result aggregation and reporting

- **`browser_automation.py`** - Browser automation services
  - Chrome extension communication
  - Test execution in browser context

### `/models` - Data Models
Pydantic models for request/response schemas and data validation

### `/prompt_refinement` - Refinement Engine
AI prompt refinement algorithms and configurations:
- **`analyzer.py`** - Prompt analysis logic and scoring
- **`config.py`** - Configuration settings and thresholds

### `/vector_db` - Vector Database Layer
Vector database client implementations:
- **`pinecone_client.py`** - Pinecone integration with Gemini embeddings

### `/testcases` - Test Case Storage
JSON files containing generated and executed test cases with timestamps

## Dependencies

### Core Framework
- **FastAPI** - Modern, fast web framework for building APIs
- **Uvicorn** - Lightning-fast ASGI server implementation
- **Pydantic** - Data validation using Python type annotations

### AI & Machine Learning
- **Google Generative AI** - Primary AI model integration (Gemini 2.5 Pro/Flash)
- **OpenAI** - Client library for OpenAI-compatible API endpoints
- **Pinecone** - Production-ready vector database for embeddings

### Document Processing
- **PyPDF2** - PDF document processing and text extraction
- **python-docx** - Microsoft Word document processing
- **python-multipart** - File upload handling

### Utilities
- **Python-dotenv** - Environment variable management
- **Requests** - HTTP client for external API calls
- **UUID** - Unique identifier generation

## Setup & Usage

### Prerequisites
- Python 3.12 or higher
- pip package manager
- API keys for required services

### Installation
```bash
# Clone the repository
cd backend

# Install dependencies
pip install -r requirements.txt
```

### Environment Variables
Create a `.env` file in the backend directory with the following configuration:

```bash
# Primary AI Provider (Gemini)
GEMINI_API_KEY=your_gemini_api_key

# Vector Database
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=drcode

# OpenAI Compatibility Layer (maps to Gemini)
OPENAI_API_KEY=your_gemini_api_key
OPENAI_API_URL=https://generativelanguage.googleapis.com/v1beta/openai

# Model Configuration
DEFAULT_PLAN_MODEL=gemini-2.5-flash
DEFAULT_NAVIGATION_MODEL=gemini-2.5-flash
DEFAULT_VALIDATION_MODEL=gemini-2.5-flash

# Optional: Custom configurations
# LOG_LEVEL=INFO
# MAX_TOKENS=4096
```

### Running the Server

#### Development Mode
```bash
# Start with auto-reload
python main.py

# Alternative startup script
python start_server.py
```

#### Production Mode
```bash
# Using uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8001 --workers 4

# Using gunicorn (recommended for production)
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8001
```

### API Documentation
Once running, access interactive documentation at:
- **Swagger UI**: `http://localhost:8001/docs`
- **ReDoc**: `http://localhost:8001/redoc`
- **Health Check**: `http://localhost:8001/api/v1/health`

## API Endpoints

### Health & Monitoring
- **`GET /api/v1/health`** - Service health status and uptime
- **`GET /api/v1/ping`** - Simple ping endpoint for connectivity checks
- **`GET /api/v1/token-usage`** - Get AI model token usage statistics
- **`DELETE /api/v1/token-usage`** - Clear token usage data

### Auto-Refinement
- **`POST /api/v1/auto-refine`** - Automatically refine prompts using AI and vector context
- **`GET /api/v1/auto-refine/health`** - Auto-refinement service health check

### Document Evaluation
- **`POST /api/v1/document-evaluation/evaluate`** - Evaluate document quality and content

### Vector Database Operations

#### Document Management
- **`POST /api/v1/documents`** - Add single document to vector database
- **`POST /api/v1/documents/batch`** - Add multiple documents in batch
- **`POST /api/v1/documents/upload`** - Upload file and extract text (legacy)

#### File Management (New Schema)
- **`POST /api/v1/files/upload`** - Upload file with user/project context
- **`GET /api/v1/files/list`** - List files for user/project
- **`DELETE /api/v1/files/{file_id}`** - Delete specific file and its chunks

#### Search & Query
- **`POST /api/v1/search`** - Search documents (legacy endpoint)
- **`POST /api/v1/query/project`** - Search within specific project
- **`POST /api/v1/query/file`** - Search within specific file

### Test Case Management

#### Generation & Retrieval
- **`POST /api/v1/testcases/generate`** - Generate AI-powered test cases
- **`GET /api/v1/testcases/`** - List all test cases with pagination
- **`GET /api/v1/testcases/{test_id}`** - Get specific test case details
- **`GET /api/v1/testcases/summary/stats`** - Get execution statistics and summary

#### Execution & Status
- **`POST /api/v1/testcases/execute`** - Execute multiple test cases
- **`POST /api/v1/testcases/run-single/{test_id}`** - Execute single test case
- **`PUT /api/v1/testcases/status`** - Update test case execution status

### AI Model Proxy
- **`POST /chat/completions`** - OpenAI-compatible chat completions endpoint (proxies to Gemini)

## API Usage Examples

### Auto-Refinement
```bash
# Refine a prompt automatically
curl -X POST "http://localhost:8001/api/v1/auto-refine" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a login form"
  }'
```

### Vector Database - File Upload
```bash
# Upload a file with context
curl -X POST "http://localhost:8001/api/v1/files/upload" \
  -F "file=@document.pdf" \
  -F "user_id=demo_user" \
  -F "project_id=demo_project"
```

### Vector Database - Search
```bash
# Search within a project
curl -X POST "http://localhost:8001/api/v1/query/project" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "user authentication",
    "user_id": "demo_user",
    "project_id": "demo_project",
    "top_k": 5
  }'
```

### Test Case Generation
```bash
# Generate test cases
curl -X POST "http://localhost:8001/api/v1/testcases/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "user_prompt": "A login form with username, password, and remember me checkbox",
    "generate_count": 5
  }'
```

### Test Case Execution
```bash
# Execute test cases
curl -X POST "http://localhost:8001/api/v1/testcases/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "test_ids": ["test_20240101_120000_1", "test_20240101_120000_2"],
    "target_url": "https://example.com"
  }'
```

### Token Usage Monitoring
```bash
# Get token usage statistics
curl -X GET "http://localhost:8001/api/v1/token-usage"

# Clear token usage data
curl -X DELETE "http://localhost:8001/api/v1/token-usage"
```

## Development

### Best Practices
- **FastAPI Standards**: Follows FastAPI best practices and conventions
- **Async/Await**: Non-blocking operations for better performance
- **Error Handling**: Comprehensive exception handling with proper HTTP status codes
- **Data Validation**: Request/response validation using Pydantic models
- **Structured Logging**: Consistent logging throughout the application
- **Type Safety**: Full TypeScript-style type hints for Python code

### Code Organization
- **Separation of Concerns**: Clear separation between controllers, services, and models
- **Dependency Injection**: Proper dependency management and injection
- **Configuration Management**: Environment-based configuration
- **Testing**: Unit and integration tests for critical functionality

### Performance Considerations
- **Async Operations**: All I/O operations are asynchronous
- **Connection Pooling**: Efficient database connection management
- **Caching**: Strategic caching for frequently accessed data
- **Rate Limiting**: Built-in protection against abuse

## Docker Deployment

### Using Docker Compose (Recommended)
```bash
# Build and start the service
docker-compose up --build

# Run in background
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop the service
docker-compose down
```

### Using Docker Directly
```bash
# Build the image
docker build -t drcode-backend .

# Run the container
docker run -p 8001:8001 \
  -e GEMINI_API_KEY=your_key \
  -e PINECONE_API_KEY=your_key \
  -v $(pwd)/screenshots:/app/screenshots \
  -v $(pwd)/testcases:/app/testcases \
  drcode-backend
```

## Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using port 8001
lsof -i :8001

# Kill the process
kill -9 <PID>

# Or use a different port
uvicorn main:app --port 8002
```

#### Environment Variables Not Loaded
```bash
# Verify .env file exists and has correct format
cat .env

# Check if variables are loaded
python -c "import os; print(os.getenv('GEMINI_API_KEY'))"
```

#### Pinecone Connection Issues
```bash
# Test Pinecone connectivity
python -c "
import pinecone
from pinecone import Pinecone
pc = Pinecone(api_key='your_key')
print(pc.list_indexes())
"
```

#### Gemini API Issues
```bash
# Test Gemini API
python -c "
import google.generativeai as genai
genai.configure(api_key='your_key')
model = genai.GenerativeModel('gemini-2.5-flash')
response = model.generate_content('Hello')
print(response.text)
"
```

### Logging and Debugging
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python main.py

# View logs in real-time
tail -f logs/app.log

# Check specific component logs
grep "vector_db" logs/app.log
grep "testcase" logs/app.log
```

### Performance Monitoring
```bash
# Monitor API performance
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8001/api/v1/health"

# Check memory usage
ps aux | grep python

# Monitor token usage
curl "http://localhost:8001/api/v1/token-usage"
```

## Production Deployment

### Security Considerations
- Use environment variables instead of .env files
- Configure proper CORS settings for production domains
- Implement API rate limiting
- Use HTTPS with proper SSL certificates
- Set up proper logging and monitoring

### Scaling
- Use multiple worker processes with Gunicorn
- Implement load balancing with nginx or similar
- Consider horizontal scaling with container orchestration
- Monitor and optimize database connections

### Monitoring
- Set up health check endpoints monitoring
- Implement error tracking (e.g., Sentry)
- Monitor API performance and response times
- Track token usage and costs
