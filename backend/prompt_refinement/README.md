# Prompt Refinement

## Overview
This directory contains the **AI-powered prompt refinement system** that analyzes user prompts and provides intelligent suggestions for improvement. It uses vector similarity search and large language models to enhance prompt quality.

## Architecture
- **Analyzer**: Core prompt analysis and refinement logic
- **Config**: Configuration settings and parameters
- **Vector Integration**: Utilizes vector database for similarity matching
- **AI Models**: Google Gemini integration for refinement suggestions

## Components

### `analyzer.py`
**Purpose**: Main prompt analysis and refinement engine

**Key Features**:
- **Prompt Analysis**: Evaluates prompt clarity, specificity, and completeness
- **Vector Similarity**: Searches for similar prompts in the database
- **AI-Powered Refinement**: Uses Gemini models for intelligent suggestions
- **Context Awareness**: Considers domain-specific requirements

**Core Functions**:
- Prompt quality assessment
- Similarity matching against historical prompts
- Refinement suggestion generation
- Context-based improvement recommendations

### `config.py`
**Purpose**: Configuration management for refinement parameters

**Configuration Areas**:
- **AI Model Settings**: Model selection, temperature, max tokens
- **Vector Database**: Index names, similarity thresholds
- **Refinement Rules**: Quality criteria, scoring weights
- **Performance Tuning**: Batch sizes, timeout settings

## Refinement Process

### 1. Prompt Analysis
```python
# Analyze prompt for quality metrics
analysis = analyzer.analyze_prompt(user_prompt)
# Returns: clarity_score, specificity_score, completeness_score
```

### 2. Vector Similarity Search
```python
# Find similar prompts in vector database
similar_prompts = analyzer.find_similar_prompts(prompt_embedding)
# Returns: List of similar prompts with similarity scores
```

### 3. AI-Powered Refinement
```python
# Generate refinement suggestions using AI
suggestions = analyzer.generate_refinements(prompt, context)
# Returns: List of improvement suggestions with explanations
```

### 4. Quality Scoring
```python
# Calculate overall prompt quality score
quality_score = analyzer.calculate_quality_score(prompt_analysis)
# Returns: Numerical quality score (0-100)
```

## Integration Points

### Vector Database
- **Pinecone Integration**: Stores and retrieves prompt embeddings
- **Similarity Search**: Finds contextually similar prompts
- **Embedding Generation**: Converts text to vector representations

### AI Models
- **OpenAI GPT**: Primary refinement model
- **Google Generative AI**: Alternative AI provider
- **Custom Prompts**: Specialized system prompts for refinement

### Service Layer
- **Auto-Refinement Service**: Main service interface
- **API Controllers**: HTTP endpoint handling
- **Background Processing**: Async refinement operations

## Usage Examples

### Basic Refinement
```python
analyzer = PromptAnalyzer()
refinement = await analyzer.refine_prompt(
    prompt="Test the login",
    context={"domain": "web_testing", "framework": "selenium"}
)
```

### Quality Assessment
```python
quality_analysis = analyzer.analyze_prompt_quality(prompt)
if quality_analysis.score < 70:
    suggestions = analyzer.get_improvement_suggestions(prompt)
```

## Configuration Options
- **Similarity Threshold**: Minimum similarity for prompt matching
- **Refinement Depth**: Number of refinement iterations
- **Model Parameters**: Temperature, max tokens, model selection
- **Quality Thresholds**: Minimum acceptable quality scores
